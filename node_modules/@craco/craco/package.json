{"name": "@craco/craco", "description": "Create React App Configuration Override, an easy and comprehensible configuration layer for create-react-app.", "version": "7.1.0", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc", "prepack": "npm run build"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/dilanx/craco.git", "directory": "packages/craco"}, "keywords": ["react", "create-react-app", "cra"], "author": "<PERSON><PERSON> (https://www.dilanxd.com)", "contributors": ["Groupe Sharegate inc."], "license": "Apache-2.0", "bugs": {"url": "https://github.com/dilanx/craco/issues"}, "homepage": "https://craco.js.org", "engines": {"node": ">=6"}, "bin": {"craco": "./dist/bin/craco.js"}, "peerDependencies": {"react-scripts": "^5.0.0"}, "devDependencies": {"@babel/types": "^7.19.3", "@craco/types": "^7.1.0", "@jest/types": "^27.5.1", "@types/cross-spawn": "^6.0.2", "@types/eslint": "^8.4.6", "@types/jest": "^27.5.2", "@types/lodash": "^4.14.186", "@types/semver": "^7.3.12", "eslint-webpack-plugin": "^3.2.0", "jest": "^27.5.1", "react-scripts": "5.*", "ts-jest": "^27.1.5", "typescript": "^4.8.4", "webpack": "^5.74.0"}, "dependencies": {"autoprefixer": "^10.4.12", "cosmiconfig": "^7.0.1", "cosmiconfig-typescript-loader": "^1.0.0", "cross-spawn": "^7.0.3", "lodash": "^4.17.21", "semver": "^7.3.7", "webpack-merge": "^5.8.0"}, "gitHead": "70a7a2a757c38dede4df80d33b498e753f313691"}