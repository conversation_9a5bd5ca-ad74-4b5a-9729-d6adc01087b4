{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/newpaid/src/App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$(),\n  _s8 = $RefreshSig$(),\n  _s9 = $RefreshSig$(),\n  _s10 = $RefreshSig$(),\n  _s11 = $RefreshSig$(),\n  _s12 = $RefreshSig$();\nimport React, { useState, useEffect, createContext, useContext } from 'react';\nimport './App.css';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BACKEND_URL = process.env.REACT_APP_BACKEND_URL;\nconst API = `${BACKEND_URL}/api`;\n\n// Auth Context\nconst AuthContext = /*#__PURE__*/createContext();\nconst AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [loading, setLoading] = useState(true);\n  const [backendConnected, setBackendConnected] = useState(true);\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      fetchProfile();\n    } else {\n      setLoading(false);\n    }\n  }, [token]);\n  const fetchProfile = async () => {\n    try {\n      // Add timeout to prevent hanging\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout\n\n      const response = await axios.get(`${API}/auth/profile`, {\n        signal: controller.signal,\n        timeout: 5000\n      });\n      clearTimeout(timeoutId);\n      setUser(response.data);\n      setBackendConnected(true);\n    } catch (error) {\n      console.error('Failed to fetch profile:', error);\n\n      // Check if it's a network/timeout error\n      if (error.code === 'ECONNABORTED' || error.name === 'AbortError' || !error.response) {\n        setBackendConnected(false);\n        // Don't logout on network errors, just clear the loading state\n        console.warn('Backend appears to be unavailable, continuing in offline mode');\n      } else {\n        // Only logout on actual auth errors (401, 403, etc.)\n        logout();\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = (token, userData) => {\n    localStorage.setItem('token', token);\n    setToken(token);\n    setUser(userData);\n    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    setToken(null);\n    setUser(null);\n    delete axios.defaults.headers.common['Authorization'];\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: {\n      user,\n      login,\n      logout,\n      loading,\n      backendConnected\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthProvider, \"XtPQd1b0+TUsRLavElBOJksQEz4=\");\n_c = AuthProvider;\nconst useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Age Verification Modal\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AgeVerificationModal = ({\n  isOpen,\n  onVerify,\n  onCancel\n}) => {\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Age Verification Required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"You must be 18 or older to access this content.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"By clicking \\\"I am 18+\\\", you confirm that you are of legal age to view adult content.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onVerify,\n          className: \"btn btn-primary\",\n          children: \"I am 18+\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onCancel,\n          className: \"btn btn-secondary\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n\n// Auth Forms\n_c2 = AgeVerificationModal;\nconst AuthForms = () => {\n  _s3();\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    name: '',\n    age_verified: false\n  });\n  const [showAgeVerification, setShowAgeVerification] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    login\n  } = useAuth();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    if (!isLogin && !formData.age_verified) {\n      setShowAgeVerification(true);\n      return;\n    }\n    try {\n      const endpoint = isLogin ? '/auth/login' : '/auth/register';\n      const response = await axios.post(`${API}${endpoint}`, formData);\n      login(response.data.access_token, response.data.user);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      // Handle different error response formats\n      let errorMessage = 'Authentication failed';\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        if (Array.isArray(error.response.data.detail)) {\n          // Handle FastAPI validation errors\n          errorMessage = error.response.data.detail.map(err => err.msg).join(', ');\n        } else if (typeof error.response.data.detail === 'string') {\n          errorMessage = error.response.data.detail;\n        } else {\n          errorMessage = JSON.stringify(error.response.data.detail);\n        }\n      }\n      setError(errorMessage);\n    }\n  };\n  const handleAgeVerification = verified => {\n    setShowAgeVerification(false);\n    if (verified) {\n      setFormData({\n        ...formData,\n        age_verified: true\n      });\n    }\n  };\n  const handleGoogleLogin = () => {\n    const popup = window.open(`${API}/auth/google/login`, 'google-login', 'width=500,height=600,scrollbars=yes,resizable=yes');\n    const handleMessage = event => {\n      // Allow messages from the backend server\n      if (event.origin !== window.location.origin && event.origin !== BACKEND_URL) return;\n      if (event.data.token && event.data.user) {\n        login(event.data.token, event.data.user);\n        if (popup && !popup.closed) {\n          popup.close();\n        }\n        window.removeEventListener('message', handleMessage);\n      }\n    };\n    window.addEventListener('message', handleMessage);\n\n    // Check if popup was closed manually\n    const checkClosed = setInterval(() => {\n      if (popup && popup.closed) {\n        clearInterval(checkClosed);\n        window.removeEventListener('message', handleMessage);\n      }\n    }, 1000);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: isLogin ? 'Sign In' : 'Create Account'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [!isLogin && /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Full Name\",\n          value: formData.name,\n          onChange: e => setFormData({\n            ...formData,\n            name: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          placeholder: \"Email\",\n          value: formData.email,\n          onChange: e => setFormData({\n            ...formData,\n            email: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"password\",\n          placeholder: \"Password\",\n          value: formData.password,\n          onChange: e => setFormData({\n            ...formData,\n            password: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary\",\n          children: isLogin ? 'Sign In' : 'Create Account'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-divider\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"or\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleGoogleLogin,\n        className: \"btn btn-google\",\n        children: \"Continue with Google\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"auth-switch\",\n        children: [isLogin ? \"Don't have an account?\" : \"Already have an account?\", /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsLogin(!isLogin),\n          className: \"link-button\",\n          children: isLogin ? 'Sign Up' : 'Sign In'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AgeVerificationModal, {\n      isOpen: showAgeVerification,\n      onVerify: () => handleAgeVerification(true),\n      onCancel: () => handleAgeVerification(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 188,\n    columnNumber: 5\n  }, this);\n};\n\n// Video Player Component\n_s3(AuthForms, \"CYudKWb1b2ZQ9FkexofOVYM1vjM=\", false, function () {\n  return [useAuth];\n});\n_c3 = AuthForms;\nconst VideoPlayer = ({\n  videoId,\n  onClose\n}) => {\n  _s4();\n  const [videoUrl, setVideoUrl] = useState('');\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    const url = `${API}/videos/${videoId}/stream?token=${encodeURIComponent(token)}`;\n    setVideoUrl(url);\n    setLoading(false);\n  }, [videoId]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-player-modal\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-player-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"close-button\",\n        onClick: onClose,\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading video...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"video\", {\n        controls: true,\n        autoPlay: true,\n        className: \"video-player\",\n        children: [/*#__PURE__*/_jsxDEV(\"source\", {\n          src: videoUrl,\n          type: \"video/mp4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), \"Your browser does not support the video tag.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 261,\n    columnNumber: 5\n  }, this);\n};\n\n// Video Card Component\n_s4(VideoPlayer, \"fUH0NcPkSjtuW/n1Qu0VY4/e8xY=\");\n_c4 = VideoPlayer;\nconst VideoCard = ({\n  video,\n  onPlay,\n  onApprove,\n  onReject,\n  isAdmin\n}) => {\n  const handleCardClick = e => {\n    // Don't trigger video play if clicking on admin buttons\n    if (e.target.closest('.admin-actions')) {\n      return;\n    }\n    onPlay(video.id);\n  };\n  const thumbnailUrl = video.thumbnail ? `${API}/videos/${video.id}/thumbnail` : \"https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=300&h=200&fit=crop\";\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-card\",\n    onClick: handleCardClick,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-thumbnail\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: thumbnailUrl,\n        alt: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"play-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"play-button\",\n          children: \"\\u25B6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), video.status === 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-status pending\",\n        children: \"Pending\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this), video.status === 'rejected' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-status rejected\",\n        children: \"Rejected\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: video.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-meta\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"category\",\n          children: video.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"views\",\n          children: [video.views, \" views\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-tags\",\n        children: video.tags.map(tag => /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tag\",\n          children: tag\n        }, tag, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), isAdmin && video.status === 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onApprove(video.id),\n          className: \"btn btn-success btn-sm\",\n          children: \"Approve\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onReject(video.id),\n          className: \"btn btn-danger btn-sm\",\n          children: \"Reject\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 292,\n    columnNumber: 5\n  }, this);\n};\n\n// Admin Page Component\n_c5 = VideoCard;\nconst AdminPage = ({\n  onBack,\n  onShowUpload\n}) => {\n  _s5();\n  const [adminVideos, setAdminVideos] = useState([]);\n  const [editingVideo, setEditingVideo] = useState(null);\n  const [users, setUsers] = useState([]);\n  const [activeTab, setActiveTab] = useState('videos');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  useEffect(() => {\n    if (activeTab === 'videos') {\n      fetchAdminVideos();\n    } else if (activeTab === 'users') {\n      fetchUsers();\n    }\n  }, [activeTab]);\n  const fetchAdminVideos = async () => {\n    try {\n      const response = await axios.get(`${API}/videos`);\n      setAdminVideos(response.data);\n    } catch (error) {\n      console.error('Failed to fetch videos:', error);\n      setError('Failed to load videos');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchUsers = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get(`${API}/admin/users`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setUsers(response.data);\n    } catch (error) {\n      console.error('Failed to fetch users:', error);\n      setError('Failed to load users');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleMakeAdmin = async userId => {\n    if (!window.confirm('Are you sure you want to grant admin access to this user?')) {\n      return;\n    }\n    try {\n      const token = localStorage.getItem('token');\n      await axios.post(`${API}/admin/users/${userId}/make-admin`, {}, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      fetchUsers(); // Refresh the user list\n      alert('User granted admin access successfully');\n    } catch (error) {\n      console.error('Failed to make user admin:', error);\n      alert('Failed to grant admin access');\n    }\n  };\n  const handleApproveUser = async userId => {\n    try {\n      const token = localStorage.getItem('token');\n      await axios.post(`${API}/admin/users/${userId}/approve`, {}, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      fetchUsers(); // Refresh the user list\n      alert('User approved successfully');\n    } catch (error) {\n      console.error('Failed to approve user:', error);\n      alert('Failed to approve user');\n    }\n  };\n  const handleEditVideo = video => {\n    setEditingVideo(video);\n  };\n  const handleUpdateVideo = async (videoId, updateData) => {\n    try {\n      await axios.put(`${API}/videos/${videoId}`, updateData);\n      setEditingVideo(null);\n      fetchAdminVideos();\n    } catch (error) {\n      console.error('Failed to update video:', error);\n      alert('Failed to update video');\n    }\n  };\n  const handleDeleteVideo = async videoId => {\n    if (!window.confirm('Are you sure you want to delete this video? This action cannot be undone.')) {\n      return;\n    }\n    try {\n      await axios.delete(`${API}/videos/${videoId}`);\n      fetchAdminVideos();\n    } catch (error) {\n      console.error('Failed to delete video:', error);\n      alert('Failed to delete video');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading admin panel...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Admin Panel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-header-actions\",\n        children: [activeTab === 'videos' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onShowUpload,\n          className: \"btn btn-primary\",\n          children: \"Upload Video\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onBack,\n          className: \"btn btn-secondary\",\n          children: \"Back to Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'videos' ? 'active' : ''}`,\n        onClick: () => setActiveTab('videos'),\n        children: \"Video Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `tab-button ${activeTab === 'users' ? 'active' : ''}`,\n        onClick: () => setActiveTab('users'),\n        children: \"User Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 19\n    }, this), activeTab === 'videos' ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-videos-grid\",\n      children: adminVideos.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-videos\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No videos found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 15\n      }, this) : adminVideos.map(video => /*#__PURE__*/_jsxDEV(AdminVideoCard, {\n        video: video,\n        onEdit: handleEditVideo,\n        onDelete: handleDeleteVideo\n      }, video.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 17\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-users-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"All Users\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"users-table\",\n        children: users.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-users\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No users found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 17\n        }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"users-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Approved\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: users.map(user => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.is_admin ? 'Yes' : 'No'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: user.is_approved ? 'Yes' : 'No'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [!user.is_admin && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleMakeAdmin(user.id),\n                  className: \"btn btn-primary btn-sm\",\n                  children: \"Make Admin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 29\n                }, this), !user.is_approved && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleApproveUser(user.id),\n                  className: \"btn btn-success btn-sm\",\n                  style: {\n                    marginLeft: '5px'\n                  },\n                  children: \"Approve\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 29\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 25\n              }, this)]\n            }, user.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 23\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 11\n    }, this), editingVideo && /*#__PURE__*/_jsxDEV(EditVideoModal, {\n      video: editingVideo,\n      onClose: () => setEditingVideo(null),\n      onUpdate: handleUpdateVideo\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 11\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 451,\n    columnNumber: 7\n  }, this);\n};\n\n// Admin Video Card Component\n_s5(AdminPage, \"6TW4Xv0rBd/IfMIrFJvbq3+SWuQ=\");\n_c6 = AdminPage;\nconst AdminVideoCard = ({\n  video,\n  onEdit,\n  onDelete\n}) => {\n  const thumbnailUrl = video.thumbnail ? `${API}/videos/${video.id}/thumbnail` : \"https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=300&h=200&fit=crop\";\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-video-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-thumbnail\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: thumbnailUrl,\n        alt: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `video-status ${video.status}`,\n        children: video.status\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: video.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-meta\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"category\",\n          children: video.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"views\",\n          children: [video.views, \" views\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-tags\",\n        children: video.tags.map(tag => /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tag\",\n          children: tag\n        }, tag, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"admin-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onEdit(video),\n          className: \"btn btn-primary btn-sm\",\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onDelete(video.id),\n          className: \"btn btn-danger btn-sm\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 590,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 570,\n    columnNumber: 5\n  }, this);\n};\n\n// Edit Video Modal Component\n_c7 = AdminVideoCard;\nconst EditVideoModal = ({\n  video,\n  onClose,\n  onUpdate\n}) => {\n  _s6();\n  const [formData, setFormData] = useState({\n    title: video.title,\n    description: video.description,\n    category: video.category,\n    tags: video.tags.join(', ')\n  });\n  const [saving, setSaving] = useState(false);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setSaving(true);\n    const updateData = {\n      ...formData,\n      tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)\n    };\n    await onUpdate(video.id, updateData);\n    setSaving(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content edit-video-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Edit Video\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Video Title\",\n          value: formData.title,\n          onChange: e => setFormData({\n            ...formData,\n            title: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          placeholder: \"Video Description\",\n          value: formData.description,\n          onChange: e => setFormData({\n            ...formData,\n            description: e.target.value\n          }),\n          rows: \"4\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Category\",\n          value: formData.category,\n          onChange: e => setFormData({\n            ...formData,\n            category: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Tags (comma separated)\",\n          value: formData.tags,\n          onChange: e => setFormData({\n            ...formData,\n            tags: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary\",\n            disabled: saving,\n            children: saving ? 'Saving...' : 'Save Changes'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onClose,\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 637,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 634,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 633,\n    columnNumber: 5\n  }, this);\n};\n\n// Upload Form Component\n_s6(EditVideoModal, \"73L0d1P8+fzzjB9ER8jj7fvPlq4=\");\n_c8 = EditVideoModal;\nconst UploadForm = ({\n  onClose,\n  onSuccess\n}) => {\n  _s7();\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    category: '',\n    tags: ''\n  });\n  const [file, setFile] = useState(null);\n  const [thumbnail, setThumbnail] = useState(null);\n  const [uploading, setUploading] = useState(false);\n  const [error, setError] = useState('');\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!file) {\n      setError('Please select a video file');\n      return;\n    }\n    setUploading(true);\n    setError('');\n    const uploadData = new FormData();\n    uploadData.append('title', formData.title);\n    uploadData.append('description', formData.description);\n    uploadData.append('category', formData.category);\n    uploadData.append('tags', formData.tags);\n    uploadData.append('file', file);\n    if (thumbnail) {\n      uploadData.append('thumbnail', thumbnail);\n    }\n    try {\n      await axios.post(`${API}/videos/upload`, uploadData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      onSuccess();\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      // Handle different error response formats\n      let errorMessage = 'Upload failed';\n      if ((_error$response2 = error.response) !== null && _error$response2 !== void 0 && (_error$response2$data = _error$response2.data) !== null && _error$response2$data !== void 0 && _error$response2$data.detail) {\n        if (Array.isArray(error.response.data.detail)) {\n          // Handle FastAPI validation errors\n          errorMessage = error.response.data.detail.map(err => err.msg).join(', ');\n        } else if (typeof error.response.data.detail === 'string') {\n          errorMessage = error.response.data.detail;\n        } else {\n          errorMessage = JSON.stringify(error.response.data.detail);\n        }\n      }\n      setError(errorMessage);\n    } finally {\n      setUploading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content upload-modal\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Upload Video\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 742,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 743,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Video Title\",\n          value: formData.title,\n          onChange: e => setFormData({\n            ...formData,\n            title: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          placeholder: \"Video Description\",\n          value: formData.description,\n          onChange: e => setFormData({\n            ...formData,\n            description: e.target.value\n          }),\n          rows: \"4\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 753,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Category\",\n          value: formData.category,\n          onChange: e => setFormData({\n            ...formData,\n            category: e.target.value\n          }),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 760,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Tags (comma separated)\",\n          value: formData.tags,\n          onChange: e => setFormData({\n            ...formData,\n            tags: e.target.value\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 767,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          accept: \"video/*\",\n          onChange: e => setFile(e.target.files[0]),\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          accept: \"image/*\",\n          onChange: e => setThumbnail(e.target.files[0]),\n          placeholder: \"Thumbnail (optional)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"thumbnail\",\n          className: \"file-label\",\n          children: \"Thumbnail (optional)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: uploading,\n            className: \"btn btn-primary\",\n            children: uploading ? 'Uploading...' : 'Upload Video'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onClose,\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 787,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 745,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 741,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 740,\n    columnNumber: 5\n  }, this);\n};\n\n// Main App Component\n_s7(UploadForm, \"GmkfayfbiAdP+I9huSCzHPP1Mts=\");\n_c9 = UploadForm;\nconst MainApp = () => {\n  _s8();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [videos, setVideos] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [currentVideo, setCurrentVideo] = useState(null);\n  const [showUpload, setShowUpload] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState('home');\n  useEffect(() => {\n    fetchVideos();\n    fetchCategories();\n  }, [selectedCategory]);\n  const fetchVideos = async () => {\n    try {\n      const params = selectedCategory ? {\n        category: selectedCategory\n      } : {};\n      const response = await axios.get(`${API}/videos`, {\n        params\n      });\n      setVideos(response.data || []);\n    } catch (error) {\n      console.error('Failed to fetch videos:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchCategories = async () => {\n    try {\n      const response = await axios.get(`${API}/categories`);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Failed to fetch categories:', error);\n      setCategories([]);\n    }\n  };\n  const handleSearch = async () => {\n    if (!searchQuery.trim()) {\n      fetchVideos();\n      return;\n    }\n    try {\n      const response = await axios.post(`${API}/search`, {\n        query: searchQuery,\n        category: selectedCategory || null\n      });\n      setVideos(response.data || []);\n    } catch (error) {\n      console.error('Search failed:', error);\n    }\n  };\n  const handleUploadSuccess = () => {\n    setShowUpload(false);\n    if (currentPage === 'admin') {\n      // If on admin page, we'll need to refresh admin videos\n      // This will be handled by the AdminPage component's useEffect\n      window.location.reload(); // Simple refresh for now\n    } else {\n      fetchVideos(); // Refresh the video list for home page\n    }\n  };\n  const handleApproveVideo = async videoId => {\n    try {\n      await axios.post(`${API}/videos/${videoId}/approve`);\n      fetchVideos(); // Refresh the video list\n    } catch (error) {\n      console.error('Failed to approve video:', error);\n      alert('Failed to approve video');\n    }\n  };\n  const handleRejectVideo = async videoId => {\n    try {\n      await axios.post(`${API}/videos/${videoId}/reject`);\n      fetchVideos(); // Refresh the video list\n    } catch (error) {\n      console.error('Failed to reject video:', error);\n      alert('Failed to reject video');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 889,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"logo\",\n          onClick: () => setCurrentPage('home'),\n          style: {\n            cursor: 'pointer'\n          },\n          children: \"Bluefilmx\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 897,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-actions\",\n          children: [(user === null || user === void 0 ? void 0 : user.is_admin) && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setCurrentPage('admin'),\n            className: `btn ${currentPage === 'admin' ? 'btn-primary' : 'btn-secondary'}`,\n            children: \"Admin Panel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-menu\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Welcome, \", user === null || user === void 0 ? void 0 : user.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 908,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: logout,\n              className: \"btn btn-secondary\",\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 909,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 896,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 895,\n      columnNumber: 7\n    }, this), currentPage === 'admin' ? /*#__PURE__*/_jsxDEV(AdminPage, {\n      onBack: () => setCurrentPage('home'),\n      onShowUpload: () => setShowUpload(true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 917,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"hero\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Premium Adult Entertainment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Stream high-quality content in a safe, secure environment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 927,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-image\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"https://images.unsplash.com/photo-1717295248358-4b8f2c8989d6?w=1200&h=400&fit=crop\",\n            alt: \"Premium streaming\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 929,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 924,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"search-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-bar\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search videos...\",\n              value: searchQuery,\n              onChange: e => setSearchQuery(e.target.value),\n              onKeyPress: e => e.key === 'Enter' && handleSearch()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 938,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSearch,\n              className: \"btn btn-primary\",\n              children: \"Search\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedCategory,\n              onChange: e => setSelectedCategory(e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All Categories\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 952,\n                columnNumber: 19\n              }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: category,\n                children: category\n              }, category, false, {\n                fileName: _jsxFileName,\n                lineNumber: 954,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 947,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 936,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 935,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"video-grid\",\n        children: videos.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-videos\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No videos found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Try adjusting your search or filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 966,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 964,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"videos-container\",\n          children: videos.map(video => /*#__PURE__*/_jsxDEV(VideoCard, {\n            video: video,\n            onPlay: setCurrentVideo,\n            onApprove: handleApproveVideo,\n            onReject: handleRejectVideo,\n            isAdmin: (user === null || user === void 0 ? void 0 : user.is_admin) || false\n          }, video.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 971,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 969,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 962,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), currentVideo && /*#__PURE__*/_jsxDEV(VideoPlayer, {\n      videoId: currentVideo,\n      onClose: () => setCurrentVideo(null)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 988,\n      columnNumber: 9\n    }, this), showUpload && /*#__PURE__*/_jsxDEV(UploadForm, {\n      onClose: () => setShowUpload(false),\n      onSuccess: handleUploadSuccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 995,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 893,\n    columnNumber: 5\n  }, this);\n};\n\n// Public Video Card Component (for unauthenticated users)\n_s8(MainApp, \"q+NOVJjBLH2ktsD+E8UTP1vU0to=\", false, function () {\n  return [useAuth];\n});\n_c10 = MainApp;\nconst PublicVideoCard = ({\n  video,\n  onPlay,\n  onShowAuth\n}) => {\n  const thumbnailUrl = video.thumbnail ? `${BACKEND_URL}/api/videos/${video.id}/thumbnail` : \"https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=300&h=200&fit=crop\";\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"video-card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-thumbnail\",\n      onClick: () => onPlay(video.id),\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: thumbnailUrl,\n        alt: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1013,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"play-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"play-button\",\n          children: \"\\u25B6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1018,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1017,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-duration\",\n        children: video.duration || '00:00'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1020,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1012,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"video-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"video-title\",\n        children: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1023,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"video-description\",\n        children: video.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1024,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-meta\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"video-category\",\n          children: video.category\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1026,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"video-views\",\n          children: [video.views, \" views\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1027,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1025,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-tags\",\n        children: video.tags.map(tag => /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tag\",\n          children: tag\n        }, tag, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1031,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1029,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1022,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1011,\n    columnNumber: 5\n  }, this);\n};\n\n// Public Video Player Component (for unauthenticated users)\n_c11 = PublicVideoCard;\nconst PublicVideoPlayer = ({\n  videoId,\n  onClose,\n  onShowAuth\n}) => {\n  _s9();\n  const [showSignInPrompt, setShowSignInPrompt] = useState(false);\n  useEffect(() => {\n    // Show sign-in prompt after a short delay to encourage registration\n    const timer = setTimeout(() => {\n      setShowSignInPrompt(true);\n    }, 30000); // Show after 30 seconds\n\n    return () => clearTimeout(timer);\n  }, []);\n  const videoUrl = `${BACKEND_URL}/api/videos/${videoId}/stream`;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    onClick: onClose,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content video-modal\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"modal-close\",\n        onClick: onClose,\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1057,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"video-container\",\n        children: /*#__PURE__*/_jsxDEV(\"video\", {\n          controls: true,\n          autoPlay: true,\n          className: \"video-player\",\n          src: videoUrl,\n          children: \"Your browser does not support the video tag.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1059,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1058,\n        columnNumber: 9\n      }, this), showSignInPrompt && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sign-in-prompt\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"prompt-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Enjoying the content?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Sign in to access unlimited streaming and exclusive features!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1073,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"prompt-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: onShowAuth,\n              className: \"btn btn-primary\",\n              children: \"Sign In Now\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSignInPrompt(false),\n              className: \"btn btn-secondary\",\n              children: \"Continue Watching\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1078,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1074,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1071,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1070,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1056,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1055,\n    columnNumber: 5\n  }, this);\n};\n\n// Public App Component (for unauthenticated users)\n_s9(PublicVideoPlayer, \"mUTQOYQFXSAWHt1CA/cDjAHEhdU=\");\n_c12 = PublicVideoPlayer;\nconst PublicApp = ({\n  onShowAuth\n}) => {\n  _s10();\n  const [videos, setVideos] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [currentVideo, setCurrentVideo] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [backendError, setBackendError] = useState(false);\n  const {\n    backendConnected\n  } = useAuth();\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        // Run both API calls concurrently\n        await Promise.allSettled([fetchVideos(), fetchCategories()]);\n      } catch (error) {\n        console.error('Error loading data:', error);\n      } finally {\n        // Ensure loading is always set to false\n        setLoading(false);\n      }\n    };\n    loadData();\n\n    // Fallback timeout to ensure loading state is cleared\n    const timeoutId = setTimeout(() => {\n      setLoading(false);\n    }, 10000); // 10 second maximum loading time\n\n    return () => clearTimeout(timeoutId);\n  }, []);\n  const fetchVideos = async () => {\n    try {\n      // Add timeout to prevent hanging\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 5000);\n      const response = await axios.get(`${API}/videos`, {\n        signal: controller.signal,\n        timeout: 5000\n      });\n      clearTimeout(timeoutId);\n      setVideos(response.data || []);\n      setBackendError(false);\n    } catch (error) {\n      console.error('Failed to fetch videos:', error);\n      setVideos([]);\n\n      // Check if it's a network/timeout error\n      if (error.code === 'ECONNABORTED' || error.name === 'AbortError' || !error.response) {\n        setBackendError(true);\n        console.warn('Backend appears to be unavailable for video fetching');\n      }\n    }\n    // Note: loading state is managed in useEffect\n  };\n  const fetchCategories = async () => {\n    try {\n      // Add timeout to prevent hanging\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 5000);\n      const response = await axios.get(`${API}/categories`, {\n        signal: controller.signal,\n        timeout: 5000\n      });\n      clearTimeout(timeoutId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Failed to fetch categories:', error);\n      setCategories([]);\n\n      // Check if it's a network/timeout error\n      if (error.code === 'ECONNABORTED' || error.name === 'AbortError' || !error.response) {\n        console.warn('Backend appears to be unavailable for category fetching');\n      }\n    }\n  };\n  const handleSearch = async () => {\n    try {\n      // Add timeout to prevent hanging\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 5000);\n      const response = await axios.post(`${API}/search`, {\n        query: searchQuery,\n        category: selectedCategory\n      }, {\n        signal: controller.signal,\n        timeout: 5000\n      });\n      clearTimeout(timeoutId);\n      setVideos(response.data || []);\n      setBackendError(false);\n    } catch (error) {\n      console.error('Failed to search videos:', error);\n\n      // Check if it's a network/timeout error\n      if (error.code === 'ECONNABORTED' || error.name === 'AbortError' || !error.response) {\n        setBackendError(true);\n        console.warn('Backend appears to be unavailable for search');\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1210,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [(backendError || !backendConnected) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"connection-status-banner\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"connection-status-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"connection-status-icon\",\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1219,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"connection-status-text\",\n          children: \"Backend service is currently unavailable. Some features may be limited.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1220,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1218,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1217,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"logo\",\n          children: \"Bluefilmx\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-actions\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onShowAuth,\n            className: \"btn btn-primary\",\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1232,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1229,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1228,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Premium Adult Entertainment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Stream high-quality content in a safe, secure environment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-image\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"https://images.unsplash.com/photo-1717295248358-4b8f2c8989d6?w=1200&h=400&fit=crop\",\n          alt: \"Premium streaming\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1246,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1245,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"search-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"search-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"search-bar\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search videos...\",\n            value: searchQuery,\n            onChange: e => setSearchQuery(e.target.value),\n            onKeyPress: e => e.key === 'Enter' && handleSearch()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1254,\n            columnNumber: 14\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSearch,\n            className: \"btn btn-primary\",\n            children: \"Search\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1261,\n            columnNumber: 14\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1253,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-bar\",\n          children: /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedCategory,\n            onChange: e => setSelectedCategory(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"All Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1268,\n              columnNumber: 16\n            }, this), Array.isArray(categories) && categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: category.name,\n              children: category.name\n            }, category.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1270,\n              columnNumber: 18\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1264,\n            columnNumber: 14\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1263,\n          columnNumber: 12\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1252,\n        columnNumber: 10\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1251,\n      columnNumber: 8\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"video-grid\",\n      children: !Array.isArray(videos) || videos.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-videos\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No videos found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1281,\n          columnNumber: 14\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Try adjusting your search or filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1282,\n          columnNumber: 14\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1280,\n        columnNumber: 12\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"videos-container\",\n        children: videos.map(video => /*#__PURE__*/_jsxDEV(PublicVideoCard, {\n          video: video,\n          onPlay: setCurrentVideo,\n          onShowAuth: onShowAuth\n        }, video.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1287,\n          columnNumber: 16\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1285,\n        columnNumber: 12\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1278,\n      columnNumber: 8\n    }, this), currentVideo && /*#__PURE__*/_jsxDEV(PublicVideoPlayer, {\n      videoId: currentVideo,\n      onClose: () => setCurrentVideo(null),\n      onShowAuth: onShowAuth\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1300,\n      columnNumber: 10\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1214,\n    columnNumber: 5\n  }, this);\n};\n\n// Auth Callback Component (for any remaining callback scenarios)\n_s10(PublicApp, \"JtM3+Gcy4wxSSofTDnv3EP85po0=\", false, function () {\n  return [useAuth];\n});\n_c13 = PublicApp;\nconst AuthCallback = () => {\n  _s11();\n  const {\n    user\n  } = useAuth();\n  useEffect(() => {\n    // If user is already authenticated, redirect to home\n    if (user) {\n      window.location.href = '/';\n    } else {\n      // If no user and no valid callback, redirect to home\n      setTimeout(() => {\n        window.location.href = '/';\n      }, 2000);\n    }\n  }, [user]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loading\",\n    children: /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Redirecting...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1328,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1327,\n    columnNumber: 5\n  }, this);\n};\n\n// Main App Router\n_s11(AuthCallback, \"C72XnuyhUicbE3VR7b9HBnFV5hM=\", false, function () {\n  return [useAuth];\n});\n_c14 = AuthCallback;\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppRouter, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1337,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1336,\n    columnNumber: 5\n  }, this);\n};\n_c15 = App;\nconst AppRouter = () => {\n  _s12();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [showAuthModal, setShowAuthModal] = useState(false);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1347,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Handle auth callback\n  if (window.location.pathname === '/auth/callback') {\n    return /*#__PURE__*/_jsxDEV(AuthCallback, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1352,\n      columnNumber: 12\n    }, this);\n  }\n\n  // If user is not logged in, show main app with limited functionality\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(PublicApp, {\n        onShowAuth: () => setShowAuthModal(true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1359,\n        columnNumber: 9\n      }, this), showAuthModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-content auth-modal\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-close\",\n            onClick: () => setShowAuthModal(false),\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1363,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(AuthForms, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1369,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1362,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1361,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  }\n  if (!user.age_verified) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Age Verification Required\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1380,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"You must verify your age to access this content.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1381,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1379,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(MainApp, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1386,\n    columnNumber: 10\n  }, this);\n};\n_s12(AppRouter, \"8AcumA47fjLgQW3UJTIdvJ3v47A=\", false, function () {\n  return [useAuth];\n});\n_c16 = AppRouter;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"AuthProvider\");\n$RefreshReg$(_c2, \"AgeVerificationModal\");\n$RefreshReg$(_c3, \"AuthForms\");\n$RefreshReg$(_c4, \"VideoPlayer\");\n$RefreshReg$(_c5, \"VideoCard\");\n$RefreshReg$(_c6, \"AdminPage\");\n$RefreshReg$(_c7, \"AdminVideoCard\");\n$RefreshReg$(_c8, \"EditVideoModal\");\n$RefreshReg$(_c9, \"UploadForm\");\n$RefreshReg$(_c10, \"MainApp\");\n$RefreshReg$(_c11, \"PublicVideoCard\");\n$RefreshReg$(_c12, \"PublicVideoPlayer\");\n$RefreshReg$(_c13, \"PublicApp\");\n$RefreshReg$(_c14, \"AuthCallback\");\n$RefreshReg$(_c15, \"App\");\n$RefreshReg$(_c16, \"AppRouter\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "createContext", "useContext", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BACKEND_URL", "process", "env", "REACT_APP_BACKEND_URL", "API", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "token", "setToken", "localStorage", "getItem", "loading", "setLoading", "backendConnected", "setBackendConnected", "defaults", "headers", "common", "fetchProfile", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "get", "signal", "timeout", "clearTimeout", "data", "error", "console", "code", "name", "warn", "logout", "login", "userData", "setItem", "removeItem", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "AgeVerificationModal", "isOpen", "onVerify", "onCancel", "className", "onClick", "_c2", "AuthForms", "_s3", "is<PERSON>ogin", "setIsLogin", "formData", "setFormData", "email", "password", "age_verified", "showAgeVerification", "setShowAgeVerification", "setError", "handleSubmit", "e", "preventDefault", "endpoint", "post", "access_token", "_error$response", "_error$response$data", "errorMessage", "detail", "Array", "isArray", "map", "err", "msg", "join", "JSON", "stringify", "handleAgeVerification", "verified", "handleGoogleLogin", "popup", "window", "open", "handleMessage", "event", "origin", "location", "closed", "close", "removeEventListener", "addEventListener", "checkClosed", "setInterval", "clearInterval", "onSubmit", "type", "placeholder", "onChange", "target", "required", "_c3", "VideoPlayer", "videoId", "onClose", "_s4", "videoUrl", "setVideoUrl", "url", "encodeURIComponent", "controls", "autoPlay", "src", "_c4", "VideoCard", "video", "onPlay", "onApprove", "onReject", "isAdmin", "handleCardClick", "closest", "id", "thumbnailUrl", "thumbnail", "alt", "title", "status", "description", "category", "views", "tags", "tag", "_c5", "AdminPage", "onBack", "onShowUpload", "_s5", "adminVideos", "setAdminVideos", "editingVideo", "setEditingVideo", "users", "setUsers", "activeTab", "setActiveTab", "fetchAdminVideos", "fetchUsers", "Authorization", "handleMakeAdmin", "userId", "confirm", "alert", "handleApproveUser", "handleEditVideo", "handleUpdateVideo", "updateData", "put", "handleDeleteVideo", "delete", "length", "AdminVideoCard", "onEdit", "onDelete", "is_admin", "is_approved", "style", "marginLeft", "EditVideoModal", "onUpdate", "_c6", "_c7", "_s6", "saving", "setSaving", "split", "trim", "filter", "rows", "disabled", "_c8", "UploadForm", "onSuccess", "_s7", "file", "setFile", "setThumbnail", "uploading", "setUploading", "uploadData", "FormData", "append", "_error$response2", "_error$response2$data", "accept", "files", "htmlFor", "_c9", "MainApp", "_s8", "videos", "setVideos", "categories", "setCategories", "currentVideo", "setCurrentVideo", "showUpload", "setShowUpload", "searchQuery", "setSearch<PERSON>uery", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "currentPage", "setCurrentPage", "fetchVideos", "fetchCategories", "params", "handleSearch", "query", "handleUploadSuccess", "reload", "handleApproveVideo", "handleRejectVideo", "cursor", "onKeyPress", "key", "_c10", "PublicVideoCard", "onShowAuth", "duration", "_c11", "PublicVideoPlayer", "_s9", "showSignInPrompt", "setShowSignInPrompt", "timer", "stopPropagation", "_c12", "PublicApp", "_s10", "backendError", "setBackendError", "loadData", "Promise", "allSettled", "_c13", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_s11", "href", "_c14", "App", "AppRouter", "_c15", "_s12", "showAuthModal", "setShowAuthModal", "pathname", "_c16", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/newpaid/src/App.js"], "sourcesContent": ["import React, { useState, useEffect, createContext, useContext } from 'react';\nimport './App.css';\nimport axios from 'axios';\n\nconst BACKEND_URL = process.env.REACT_APP_BACKEND_URL;\nconst API = `${BACKEND_URL}/api`;\n\n// Auth Context\nconst AuthContext = createContext();\n\nconst AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [loading, setLoading] = useState(true);\n  const [backendConnected, setBackendConnected] = useState(true);\n\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      fetchProfile();\n    } else {\n      setLoading(false);\n    }\n  }, [token]);\n\n  const fetchProfile = async () => {\n    try {\n      // Add timeout to prevent hanging\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout\n      \n      const response = await axios.get(`${API}/auth/profile`, {\n        signal: controller.signal,\n        timeout: 5000\n      });\n      \n      clearTimeout(timeoutId);\n      setUser(response.data);\n      setBackendConnected(true);\n    } catch (error) {\n      console.error('Failed to fetch profile:', error);\n      \n      // Check if it's a network/timeout error\n      if (error.code === 'ECONNABORTED' || error.name === 'AbortError' || !error.response) {\n        setBackendConnected(false);\n        // Don't logout on network errors, just clear the loading state\n        console.warn('Backend appears to be unavailable, continuing in offline mode');\n      } else {\n        // Only logout on actual auth errors (401, 403, etc.)\n        logout();\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = (token, userData) => {\n    localStorage.setItem('token', token);\n    setToken(token);\n    setUser(userData);\n    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    setToken(null);\n    setUser(null);\n    delete axios.defaults.headers.common['Authorization'];\n  };\n\n  return (\n    <AuthContext.Provider value={{ user, login, logout, loading, backendConnected }}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nconst useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Age Verification Modal\nconst AgeVerificationModal = ({ isOpen, onVerify, onCancel }) => {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal-overlay\">\n      <div className=\"modal-content\">\n        <h2>Age Verification Required</h2>\n        <p>You must be 18 or older to access this content.</p>\n        <p>By clicking \"I am 18+\", you confirm that you are of legal age to view adult content.</p>\n        <div className=\"modal-actions\">\n          <button onClick={onVerify} className=\"btn btn-primary\">I am 18+</button>\n          <button onClick={onCancel} className=\"btn btn-secondary\">Cancel</button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Auth Forms\nconst AuthForms = () => {\n  const [isLogin, setIsLogin] = useState(true);\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    name: '',\n    age_verified: false\n  });\n  const [showAgeVerification, setShowAgeVerification] = useState(false);\n  const [error, setError] = useState('');\n  const { login } = useAuth();\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n\n    if (!isLogin && !formData.age_verified) {\n      setShowAgeVerification(true);\n      return;\n    }\n\n    try {\n      const endpoint = isLogin ? '/auth/login' : '/auth/register';\n      const response = await axios.post(`${API}${endpoint}`, formData);\n      \n      login(response.data.access_token, response.data.user);\n    } catch (error) {\n      // Handle different error response formats\n      let errorMessage = 'Authentication failed';\n      if (error.response?.data?.detail) {\n        if (Array.isArray(error.response.data.detail)) {\n          // Handle FastAPI validation errors\n          errorMessage = error.response.data.detail.map(err => err.msg).join(', ');\n        } else if (typeof error.response.data.detail === 'string') {\n          errorMessage = error.response.data.detail;\n        } else {\n          errorMessage = JSON.stringify(error.response.data.detail);\n        }\n      }\n      setError(errorMessage);\n    }\n  };\n\n  const handleAgeVerification = (verified) => {\n    setShowAgeVerification(false);\n    if (verified) {\n      setFormData({ ...formData, age_verified: true });\n    }\n  };\n\n  const handleGoogleLogin = () => {\n    const popup = window.open(\n      `${API}/auth/google/login`,\n      'google-login',\n      'width=500,height=600,scrollbars=yes,resizable=yes'\n    );\n    \n    const handleMessage = (event) => {\n      // Allow messages from the backend server\n      if (event.origin !== window.location.origin && event.origin !== BACKEND_URL) return;\n      \n      if (event.data.token && event.data.user) {\n        login(event.data.token, event.data.user);\n        if (popup && !popup.closed) {\n          popup.close();\n        }\n        window.removeEventListener('message', handleMessage);\n      }\n    };\n    \n    window.addEventListener('message', handleMessage);\n    \n    // Check if popup was closed manually\n    const checkClosed = setInterval(() => {\n      if (popup && popup.closed) {\n        clearInterval(checkClosed);\n        window.removeEventListener('message', handleMessage);\n      }\n    }, 1000);\n  };\n\n  return (\n    <div className=\"auth-container\">\n      <div className=\"auth-card\">\n        <h2>{isLogin ? 'Sign In' : 'Create Account'}</h2>\n        \n        {error && <div className=\"error-message\">{error}</div>}\n        \n        <form onSubmit={handleSubmit}>\n          {!isLogin && (\n            <input\n              type=\"text\"\n              placeholder=\"Full Name\"\n              value={formData.name}\n              onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n              required\n            />\n          )}\n          <input\n            type=\"email\"\n            placeholder=\"Email\"\n            value={formData.email}\n            onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n            required\n          />\n          <input\n            type=\"password\"\n            placeholder=\"Password\"\n            value={formData.password}\n            onChange={(e) => setFormData({ ...formData, password: e.target.value })}\n            required\n          />\n          <button type=\"submit\" className=\"btn btn-primary\">\n            {isLogin ? 'Sign In' : 'Create Account'}\n          </button>\n        </form>\n\n        <div className=\"auth-divider\">\n          <span>or</span>\n        </div>\n\n        <button onClick={handleGoogleLogin} className=\"btn btn-google\">\n          Continue with Google\n        </button>\n\n        <p className=\"auth-switch\">\n          {isLogin ? \"Don't have an account?\" : \"Already have an account?\"}\n          <button onClick={() => setIsLogin(!isLogin)} className=\"link-button\">\n            {isLogin ? 'Sign Up' : 'Sign In'}\n          </button>\n        </p>\n      </div>\n\n      <AgeVerificationModal\n        isOpen={showAgeVerification}\n        onVerify={() => handleAgeVerification(true)}\n        onCancel={() => handleAgeVerification(false)}\n      />\n    </div>\n  );\n};\n\n// Video Player Component\nconst VideoPlayer = ({ videoId, onClose }) => {\n  const [videoUrl, setVideoUrl] = useState('');\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    const url = `${API}/videos/${videoId}/stream?token=${encodeURIComponent(token)}`;\n    setVideoUrl(url);\n    setLoading(false);\n  }, [videoId]);\n\n  return (\n    <div className=\"video-player-modal\">\n      <div className=\"video-player-content\">\n        <button className=\"close-button\" onClick={onClose}>×</button>\n        {loading ? (\n          <div className=\"loading\">Loading video...</div>\n        ) : (\n          <video controls autoPlay className=\"video-player\">\n            <source src={videoUrl} type=\"video/mp4\" />\n            Your browser does not support the video tag.\n          </video>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Video Card Component\nconst VideoCard = ({ video, onPlay, onApprove, onReject, isAdmin }) => {\n  const handleCardClick = (e) => {\n    // Don't trigger video play if clicking on admin buttons\n    if (e.target.closest('.admin-actions')) {\n      return;\n    }\n    onPlay(video.id);\n  };\n\n  const thumbnailUrl = video.thumbnail \n    ? `${API}/videos/${video.id}/thumbnail`\n    : \"https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=300&h=200&fit=crop\";\n\n  return (\n    <div className=\"video-card\" onClick={handleCardClick}>\n      <div className=\"video-thumbnail\">\n        <img \n          src={thumbnailUrl}\n          alt={video.title}\n        />\n        <div className=\"play-overlay\">\n          <div className=\"play-button\">▶</div>\n        </div>\n        {video.status === 'pending' && (\n          <div className=\"video-status pending\">Pending</div>\n        )}\n        {video.status === 'rejected' && (\n          <div className=\"video-status rejected\">Rejected</div>\n        )}\n      </div>\n      <div className=\"video-info\">\n        <h3>{video.title}</h3>\n        <p>{video.description}</p>\n        <div className=\"video-meta\">\n          <span className=\"category\">{video.category}</span>\n          <span className=\"views\">{video.views} views</span>\n        </div>\n        <div className=\"video-tags\">\n          {video.tags.map(tag => (\n            <span key={tag} className=\"tag\">{tag}</span>\n          ))}\n        </div>\n        {isAdmin && video.status === 'pending' && (\n          <div className=\"admin-actions\">\n            <button \n              onClick={() => onApprove(video.id)} \n              className=\"btn btn-success btn-sm\"\n            >\n              Approve\n            </button>\n            <button \n              onClick={() => onReject(video.id)} \n              className=\"btn btn-danger btn-sm\"\n            >\n              Reject\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Admin Page Component\nconst AdminPage = ({ onBack, onShowUpload }) => {\n  const [adminVideos, setAdminVideos] = useState([]);\n  const [editingVideo, setEditingVideo] = useState(null);\n  const [users, setUsers] = useState([]);\n  const [activeTab, setActiveTab] = useState('videos');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    if (activeTab === 'videos') {\n      fetchAdminVideos();\n    } else if (activeTab === 'users') {\n      fetchUsers();\n    }\n  }, [activeTab]);\n\n  const fetchAdminVideos = async () => {\n    try {\n      const response = await axios.get(`${API}/videos`);\n      setAdminVideos(response.data);\n    } catch (error) {\n      console.error('Failed to fetch videos:', error);\n      setError('Failed to load videos');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchUsers = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get(`${API}/admin/users`, {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n      setUsers(response.data);\n    } catch (error) {\n      console.error('Failed to fetch users:', error);\n      setError('Failed to load users');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleMakeAdmin = async (userId) => {\n    if (!window.confirm('Are you sure you want to grant admin access to this user?')) {\n      return;\n    }\n\n    try {\n      const token = localStorage.getItem('token');\n      await axios.post(`${API}/admin/users/${userId}/make-admin`, {}, {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n      fetchUsers(); // Refresh the user list\n      alert('User granted admin access successfully');\n    } catch (error) {\n      console.error('Failed to make user admin:', error);\n      alert('Failed to grant admin access');\n    }\n  };\n\n  const handleApproveUser = async (userId) => {\n    try {\n      const token = localStorage.getItem('token');\n      await axios.post(`${API}/admin/users/${userId}/approve`, {}, {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n      fetchUsers(); // Refresh the user list\n      alert('User approved successfully');\n    } catch (error) {\n      console.error('Failed to approve user:', error);\n      alert('Failed to approve user');\n    }\n  };\n\n  const handleEditVideo = (video) => {\n    setEditingVideo(video);\n  };\n\n  const handleUpdateVideo = async (videoId, updateData) => {\n    try {\n      await axios.put(`${API}/videos/${videoId}`, updateData);\n      setEditingVideo(null);\n      fetchAdminVideos();\n    } catch (error) {\n      console.error('Failed to update video:', error);\n      alert('Failed to update video');\n    }\n  };\n\n  const handleDeleteVideo = async (videoId) => {\n    if (!window.confirm('Are you sure you want to delete this video? This action cannot be undone.')) {\n      return;\n    }\n\n    try {\n      await axios.delete(`${API}/videos/${videoId}`);\n      fetchAdminVideos();\n    } catch (error) {\n      console.error('Failed to delete video:', error);\n      alert('Failed to delete video');\n    }\n  };\n\n  if (loading) {\n    return <div className=\"loading\">Loading admin panel...</div>;\n  }\n\n  return (\n      <div className=\"admin-page\">\n        <div className=\"admin-header\">\n          <h2>Admin Panel</h2>\n          <div className=\"admin-header-actions\">\n            {activeTab === 'videos' && (\n              <button onClick={onShowUpload} className=\"btn btn-primary\">\n                Upload Video\n              </button>\n            )}\n            <button onClick={onBack} className=\"btn btn-secondary\">Back to Home</button>\n          </div>\n        </div>\n\n        <div className=\"admin-tabs\">\n          <button \n            className={`tab-button ${activeTab === 'videos' ? 'active' : ''}`}\n            onClick={() => setActiveTab('videos')}\n          >\n            Video Management\n          </button>\n          <button \n            className={`tab-button ${activeTab === 'users' ? 'active' : ''}`}\n            onClick={() => setActiveTab('users')}\n          >\n            User Management\n          </button>\n        </div>\n\n        {error && <div className=\"error-message\">{error}</div>}\n\n        {activeTab === 'videos' ? (\n          <div className=\"admin-videos-grid\">\n            {adminVideos.length === 0 ? (\n              <div className=\"no-videos\">\n                <h3>No videos found</h3>\n              </div>\n            ) : (\n              adminVideos.map(video => (\n                <AdminVideoCard\n                  key={video.id}\n                  video={video}\n                  onEdit={handleEditVideo}\n                  onDelete={handleDeleteVideo}\n                />\n              ))\n            )}\n          </div>\n        ) : (\n          <div className=\"admin-users-section\">\n            <h3>All Users</h3>\n            <div className=\"users-table\">\n              {users.length === 0 ? (\n                <div className=\"no-users\">\n                  <p>No users found</p>\n                </div>\n              ) : (\n                <table className=\"users-table\">\n                  <thead>\n                    <tr>\n                      <th>Name</th>\n                      <th>Email</th>\n                      <th>Admin</th>\n                      <th>Approved</th>\n                      <th>Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {users.map(user => (\n                      <tr key={user.id}>\n                        <td>{user.name}</td>\n                        <td>{user.email}</td>\n                        <td>{user.is_admin ? 'Yes' : 'No'}</td>\n                        <td>{user.is_approved ? 'Yes' : 'No'}</td>\n                        <td>\n                          {!user.is_admin && (\n                            <button \n                              onClick={() => handleMakeAdmin(user.id)}\n                              className=\"btn btn-primary btn-sm\"\n                            >\n                              Make Admin\n                            </button>\n                          )}\n                          {!user.is_approved && (\n                            <button \n                              onClick={() => handleApproveUser(user.id)}\n                              className=\"btn btn-success btn-sm\"\n                              style={{marginLeft: '5px'}}\n                            >\n                              Approve\n                            </button>\n                          )}\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              )}\n            </div>\n          </div>\n        )}\n\n        {editingVideo && (\n          <EditVideoModal\n            video={editingVideo}\n            onClose={() => setEditingVideo(null)}\n            onUpdate={handleUpdateVideo}\n          />\n        )}\n      </div>\n  );\n};\n\n// Admin Video Card Component\nconst AdminVideoCard = ({ video, onEdit, onDelete }) => {\n  const thumbnailUrl = video.thumbnail \n    ? `${API}/videos/${video.id}/thumbnail`\n    : \"https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=300&h=200&fit=crop\";\n\n  return (\n    <div className=\"admin-video-card\">\n      <div className=\"video-thumbnail\">\n        <img \n          src={thumbnailUrl}\n          alt={video.title}\n        />\n        <div className={`video-status ${video.status}`}>{video.status}</div>\n      </div>\n      <div className=\"video-info\">\n        <h3>{video.title}</h3>\n        <p>{video.description}</p>\n        <div className=\"video-meta\">\n          <span className=\"category\">{video.category}</span>\n          <span className=\"views\">{video.views} views</span>\n        </div>\n        <div className=\"video-tags\">\n          {video.tags.map(tag => (\n            <span key={tag} className=\"tag\">{tag}</span>\n          ))}\n        </div>\n        <div className=\"admin-actions\">\n          <button \n            onClick={() => onEdit(video)} \n            className=\"btn btn-primary btn-sm\"\n          >\n            Edit\n          </button>\n          <button \n            onClick={() => onDelete(video.id)} \n            className=\"btn btn-danger btn-sm\"\n          >\n            Delete\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Edit Video Modal Component\nconst EditVideoModal = ({ video, onClose, onUpdate }) => {\n  const [formData, setFormData] = useState({\n    title: video.title,\n    description: video.description,\n    category: video.category,\n    tags: video.tags.join(', ')\n  });\n  const [saving, setSaving] = useState(false);\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setSaving(true);\n\n    const updateData = {\n      ...formData,\n      tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)\n    };\n\n    await onUpdate(video.id, updateData);\n    setSaving(false);\n  };\n\n  return (\n    <div className=\"modal-overlay\">\n      <div className=\"modal-content edit-video-modal\">\n        <h2>Edit Video</h2>\n        \n        <form onSubmit={handleSubmit}>\n          <input\n            type=\"text\"\n            placeholder=\"Video Title\"\n            value={formData.title}\n            onChange={(e) => setFormData({ ...formData, title: e.target.value })}\n            required\n          />\n          <textarea\n            placeholder=\"Video Description\"\n            value={formData.description}\n            onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n            rows=\"4\"\n            required\n          />\n          <input\n            type=\"text\"\n            placeholder=\"Category\"\n            value={formData.category}\n            onChange={(e) => setFormData({ ...formData, category: e.target.value })}\n            required\n          />\n          <input\n            type=\"text\"\n            placeholder=\"Tags (comma separated)\"\n            value={formData.tags}\n            onChange={(e) => setFormData({ ...formData, tags: e.target.value })}\n          />\n          \n          <div className=\"modal-actions\">\n            <button type=\"submit\" className=\"btn btn-primary\" disabled={saving}>\n              {saving ? 'Saving...' : 'Save Changes'}\n            </button>\n            <button type=\"button\" onClick={onClose} className=\"btn btn-secondary\">\n              Cancel\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\n// Upload Form Component\nconst UploadForm = ({ onClose, onSuccess }) => {\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    category: '',\n    tags: ''\n  });\n  const [file, setFile] = useState(null);\n  const [thumbnail, setThumbnail] = useState(null);\n  const [uploading, setUploading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    if (!file) {\n      setError('Please select a video file');\n      return;\n    }\n\n    setUploading(true);\n    setError('');\n\n    const uploadData = new FormData();\n    uploadData.append('title', formData.title);\n    uploadData.append('description', formData.description);\n    uploadData.append('category', formData.category);\n    uploadData.append('tags', formData.tags);\n    uploadData.append('file', file);\n    if (thumbnail) {\n      uploadData.append('thumbnail', thumbnail);\n    }\n\n    try {\n      await axios.post(`${API}/videos/upload`, uploadData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n      onSuccess();\n    } catch (error) {\n      // Handle different error response formats\n      let errorMessage = 'Upload failed';\n      if (error.response?.data?.detail) {\n        if (Array.isArray(error.response.data.detail)) {\n          // Handle FastAPI validation errors\n          errorMessage = error.response.data.detail.map(err => err.msg).join(', ');\n        } else if (typeof error.response.data.detail === 'string') {\n          errorMessage = error.response.data.detail;\n        } else {\n          errorMessage = JSON.stringify(error.response.data.detail);\n        }\n      }\n      setError(errorMessage);\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  return (\n    <div className=\"modal-overlay\">\n      <div className=\"modal-content upload-modal\">\n        <h2>Upload Video</h2>\n        {error && <div className=\"error-message\">{error}</div>}\n        \n        <form onSubmit={handleSubmit}>\n          <input\n            type=\"text\"\n            placeholder=\"Video Title\"\n            value={formData.title}\n            onChange={(e) => setFormData({ ...formData, title: e.target.value })}\n            required\n          />\n          <textarea\n            placeholder=\"Video Description\"\n            value={formData.description}\n            onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n            rows=\"4\"\n            required\n          />\n          <input\n            type=\"text\"\n            placeholder=\"Category\"\n            value={formData.category}\n            onChange={(e) => setFormData({ ...formData, category: e.target.value })}\n            required\n          />\n          <input\n            type=\"text\"\n            placeholder=\"Tags (comma separated)\"\n            value={formData.tags}\n            onChange={(e) => setFormData({ ...formData, tags: e.target.value })}\n          />\n          <input\n            type=\"file\"\n            accept=\"video/*\"\n            onChange={(e) => setFile(e.target.files[0])}\n            required\n          />\n          <input\n            type=\"file\"\n            accept=\"image/*\"\n            onChange={(e) => setThumbnail(e.target.files[0])}\n            placeholder=\"Thumbnail (optional)\"\n          />\n          <label htmlFor=\"thumbnail\" className=\"file-label\">Thumbnail (optional)</label>\n          \n          <div className=\"modal-actions\">\n            <button type=\"submit\" disabled={uploading} className=\"btn btn-primary\">\n              {uploading ? 'Uploading...' : 'Upload Video'}\n            </button>\n            <button type=\"button\" onClick={onClose} className=\"btn btn-secondary\">\n              Cancel\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\n// Main App Component\nconst MainApp = () => {\n  const { user, logout } = useAuth();\n  const [videos, setVideos] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [currentVideo, setCurrentVideo] = useState(null);\n  const [showUpload, setShowUpload] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState('home');\n\n  useEffect(() => {\n    fetchVideos();\n    fetchCategories();\n  }, [selectedCategory]);\n\n  const fetchVideos = async () => {\n    try {\n      const params = selectedCategory ? { category: selectedCategory } : {};\n      const response = await axios.get(`${API}/videos`, { params });\n      setVideos(response.data || []);\n    } catch (error) {\n      console.error('Failed to fetch videos:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchCategories = async () => {\n    try {\n      const response = await axios.get(`${API}/categories`);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Failed to fetch categories:', error);\n      setCategories([]);\n    }\n  };\n\n  const handleSearch = async () => {\n    if (!searchQuery.trim()) {\n      fetchVideos();\n      return;\n    }\n\n    try {\n      const response = await axios.post(`${API}/search`, {\n        query: searchQuery,\n        category: selectedCategory || null\n      });\n      setVideos(response.data || []);\n    } catch (error) {\n      console.error('Search failed:', error);\n    }\n  };\n\n  const handleUploadSuccess = () => {\n    setShowUpload(false);\n    if (currentPage === 'admin') {\n      // If on admin page, we'll need to refresh admin videos\n      // This will be handled by the AdminPage component's useEffect\n      window.location.reload(); // Simple refresh for now\n    } else {\n      fetchVideos(); // Refresh the video list for home page\n    }\n  };\n\n  const handleApproveVideo = async (videoId) => {\n    try {\n      await axios.post(`${API}/videos/${videoId}/approve`);\n      fetchVideos(); // Refresh the video list\n    } catch (error) {\n      console.error('Failed to approve video:', error);\n      alert('Failed to approve video');\n    }\n  };\n\n  const handleRejectVideo = async (videoId) => {\n    try {\n      await axios.post(`${API}/videos/${videoId}/reject`);\n      fetchVideos(); // Refresh the video list\n    } catch (error) {\n      console.error('Failed to reject video:', error);\n      alert('Failed to reject video');\n    }\n  };\n\n  if (loading) {\n    return <div className=\"loading\">Loading...</div>;\n  }\n\n  return (\n    <div className=\"app\">\n      {/* Header */}\n      <header className=\"header\">\n        <div className=\"header-content\">\n          <h1 className=\"logo\" onClick={() => setCurrentPage('home')} style={{cursor: 'pointer'}}>Bluefilmx</h1>\n          <div className=\"header-actions\">\n            {user?.is_admin && (\n              <button \n                onClick={() => setCurrentPage('admin')} \n                className={`btn ${currentPage === 'admin' ? 'btn-primary' : 'btn-secondary'}`}\n              >\n                Admin Panel\n              </button>\n            )}\n            <div className=\"user-menu\">\n              <span>Welcome, {user?.name}</span>\n              <button onClick={logout} className=\"btn btn-secondary\">Logout</button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      {currentPage === 'admin' ? (\n        <AdminPage \n          onBack={() => setCurrentPage('home')} \n          onShowUpload={() => setShowUpload(true)}\n        />\n      ) : (\n        <>\n          {/* Hero Section */}\n          <section className=\"hero\">\n            <div className=\"hero-content\">\n              <h2>Premium Adult Entertainment</h2>\n              <p>Stream high-quality content in a safe, secure environment</p>\n            </div>\n            <div className=\"hero-image\">\n              <img src=\"https://images.unsplash.com/photo-1717295248358-4b8f2c8989d6?w=1200&h=400&fit=crop\" alt=\"Premium streaming\" />\n            </div>\n          </section>\n\n          {/* Search and Filter */}\n          <section className=\"search-section\">\n            <div className=\"search-container\">\n              <div className=\"search-bar\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search videos...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n                />\n                <button onClick={handleSearch} className=\"btn btn-primary\">Search</button>\n              </div>\n              <div className=\"filter-bar\">\n                <select \n                  value={selectedCategory} \n                  onChange={(e) => setSelectedCategory(e.target.value)}\n                >\n                  <option value=\"\">All Categories</option>\n                  {categories.map(category => (\n                    <option key={category} value={category}>{category}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </section>\n\n          {/* Video Grid */}\n          <section className=\"video-grid\">\n            {videos.length === 0 ? (\n              <div className=\"no-videos\">\n                <h3>No videos found</h3>\n                <p>Try adjusting your search or filters</p>\n              </div>\n            ) : (\n              <div className=\"videos-container\">\n                {videos.map(video => (\n                  <VideoCard\n                    key={video.id}\n                    video={video}\n                    onPlay={setCurrentVideo}\n                    onApprove={handleApproveVideo}\n                    onReject={handleRejectVideo}\n                    isAdmin={user?.is_admin || false}\n                  />\n                ))}\n              </div>\n            )}\n          </section>\n        </>\n      )}\n\n      {/* Modals */}\n      {currentVideo && (\n        <VideoPlayer\n          videoId={currentVideo}\n          onClose={() => setCurrentVideo(null)}\n        />\n      )}\n\n      {showUpload && (\n        <UploadForm\n          onClose={() => setShowUpload(false)}\n          onSuccess={handleUploadSuccess}\n        />\n      )}\n    </div>\n  );\n};\n\n// Public Video Card Component (for unauthenticated users)\nconst PublicVideoCard = ({ video, onPlay, onShowAuth }) => {\n  const thumbnailUrl = video.thumbnail \n    ? `${BACKEND_URL}/api/videos/${video.id}/thumbnail`\n    : \"https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=300&h=200&fit=crop\";\n\n  return (\n    <div className=\"video-card\">\n      <div className=\"video-thumbnail\" onClick={() => onPlay(video.id)}>\n        <img \n          src={thumbnailUrl}\n          alt={video.title}\n        />\n        <div className=\"play-overlay\">\n          <div className=\"play-button\">▶</div>\n        </div>\n        <div className=\"video-duration\">{video.duration || '00:00'}</div>\n      </div>\n      <div className=\"video-info\">\n        <h3 className=\"video-title\">{video.title}</h3>\n        <p className=\"video-description\">{video.description}</p>\n        <div className=\"video-meta\">\n          <span className=\"video-category\">{video.category}</span>\n          <span className=\"video-views\">{video.views} views</span>\n        </div>\n        <div className=\"video-tags\">\n          {video.tags.map(tag => (\n            <span key={tag} className=\"tag\">{tag}</span>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Public Video Player Component (for unauthenticated users)\nconst PublicVideoPlayer = ({ videoId, onClose, onShowAuth }) => {\n  const [showSignInPrompt, setShowSignInPrompt] = useState(false);\n\n  useEffect(() => {\n    // Show sign-in prompt after a short delay to encourage registration\n    const timer = setTimeout(() => {\n      setShowSignInPrompt(true);\n    }, 30000); // Show after 30 seconds\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  const videoUrl = `${BACKEND_URL}/api/videos/${videoId}/stream`;\n\n  return (\n    <div className=\"modal-overlay\" onClick={onClose}>\n      <div className=\"modal-content video-modal\" onClick={(e) => e.stopPropagation()}>\n        <button className=\"modal-close\" onClick={onClose}>×</button>\n        <div className=\"video-container\">\n          <video \n            controls \n            autoPlay \n            className=\"video-player\"\n            src={videoUrl}\n          >\n            Your browser does not support the video tag.\n          </video>\n        </div>\n        \n        {showSignInPrompt && (\n          <div className=\"sign-in-prompt\">\n            <div className=\"prompt-content\">\n              <h3>Enjoying the content?</h3>\n              <p>Sign in to access unlimited streaming and exclusive features!</p>\n              <div className=\"prompt-actions\">\n                <button onClick={onShowAuth} className=\"btn btn-primary\">\n                  Sign In Now\n                </button>\n                <button \n                  onClick={() => setShowSignInPrompt(false)} \n                  className=\"btn btn-secondary\"\n                >\n                  Continue Watching\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Public App Component (for unauthenticated users)\nconst PublicApp = ({ onShowAuth }) => {\n  const [videos, setVideos] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('');\n  const [currentVideo, setCurrentVideo] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [backendError, setBackendError] = useState(false);\n  const { backendConnected } = useAuth();\n\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        // Run both API calls concurrently\n        await Promise.allSettled([\n          fetchVideos(),\n          fetchCategories()\n        ]);\n      } catch (error) {\n        console.error('Error loading data:', error);\n      } finally {\n        // Ensure loading is always set to false\n        setLoading(false);\n      }\n    };\n\n    loadData();\n\n    // Fallback timeout to ensure loading state is cleared\n    const timeoutId = setTimeout(() => {\n      setLoading(false);\n    }, 10000); // 10 second maximum loading time\n\n    return () => clearTimeout(timeoutId);\n  }, []);\n\n  const fetchVideos = async () => {\n    try {\n      // Add timeout to prevent hanging\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 5000);\n      \n      const response = await axios.get(`${API}/videos`, {\n        signal: controller.signal,\n        timeout: 5000\n      });\n      \n      clearTimeout(timeoutId);\n      setVideos(response.data || []);\n      setBackendError(false);\n    } catch (error) {\n      console.error('Failed to fetch videos:', error);\n      setVideos([]);\n      \n      // Check if it's a network/timeout error\n      if (error.code === 'ECONNABORTED' || error.name === 'AbortError' || !error.response) {\n        setBackendError(true);\n        console.warn('Backend appears to be unavailable for video fetching');\n      }\n    }\n    // Note: loading state is managed in useEffect\n  };\n\n  const fetchCategories = async () => {\n    try {\n      // Add timeout to prevent hanging\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 5000);\n      \n      const response = await axios.get(`${API}/categories`, {\n        signal: controller.signal,\n        timeout: 5000\n      });\n      \n      clearTimeout(timeoutId);\n      setCategories(response.data || []);\n    } catch (error) {\n      console.error('Failed to fetch categories:', error);\n      setCategories([]);\n      \n      // Check if it's a network/timeout error\n      if (error.code === 'ECONNABORTED' || error.name === 'AbortError' || !error.response) {\n        console.warn('Backend appears to be unavailable for category fetching');\n      }\n    }\n  };\n\n  const handleSearch = async () => {\n    try {\n      // Add timeout to prevent hanging\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 5000);\n      \n      const response = await axios.post(`${API}/search`, {\n        query: searchQuery,\n        category: selectedCategory\n      }, {\n        signal: controller.signal,\n        timeout: 5000\n      });\n      \n      clearTimeout(timeoutId);\n      setVideos(response.data || []);\n      setBackendError(false);\n    } catch (error) {\n      console.error('Failed to search videos:', error);\n      \n      // Check if it's a network/timeout error\n      if (error.code === 'ECONNABORTED' || error.name === 'AbortError' || !error.response) {\n        setBackendError(true);\n        console.warn('Backend appears to be unavailable for search');\n      }\n    }\n  };\n\n  if (loading) {\n    return <div className=\"loading\">Loading...</div>;\n  }\n\n  return (\n    <div className=\"app\">\n      {/* Connection Status Indicator */}\n      {(backendError || !backendConnected) && (\n        <div className=\"connection-status-banner\">\n          <div className=\"connection-status-content\">\n            <span className=\"connection-status-icon\">⚠️</span>\n            <span className=\"connection-status-text\">\n              Backend service is currently unavailable. Some features may be limited.\n            </span>\n          </div>\n        </div>\n      )}\n      \n      {/* Header */}\n      <header className=\"header\">\n        <div className=\"header-content\">\n          <h1 className=\"logo\">Bluefilmx</h1>\n          <div className=\"header-actions\">\n            <button onClick={onShowAuth} className=\"btn btn-primary\">\n              Sign In\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"hero\">\n        <div className=\"hero-content\">\n          <h2>Premium Adult Entertainment</h2>\n          <p>Stream high-quality content in a safe, secure environment</p>\n        </div>\n        <div className=\"hero-image\">\n          <img src=\"https://images.unsplash.com/photo-1717295248358-4b8f2c8989d6?w=1200&h=400&fit=crop\" alt=\"Premium streaming\" />\n        </div>\n      </section>\n\n      {/* Search and Filter */}\n       <section className=\"search-section\">\n         <div className=\"search-container\">\n           <div className=\"search-bar\">\n             <input\n               type=\"text\"\n               placeholder=\"Search videos...\"\n               value={searchQuery}\n               onChange={(e) => setSearchQuery(e.target.value)}\n               onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n             />\n             <button onClick={handleSearch} className=\"btn btn-primary\">Search</button>\n           </div>\n           <div className=\"filter-bar\">\n             <select \n               value={selectedCategory} \n               onChange={(e) => setSelectedCategory(e.target.value)}\n             >\n               <option value=\"\">All Categories</option>\n               {Array.isArray(categories) && categories.map(category => (\n                 <option key={category.id} value={category.name}>{category.name}</option>\n               ))}\n             </select>\n           </div>\n         </div>\n       </section>\n\n       {/* Video Grid */}\n       <section className=\"video-grid\">\n         {!Array.isArray(videos) || videos.length === 0 ? (\n           <div className=\"no-videos\">\n             <h3>No videos found</h3>\n             <p>Try adjusting your search or filters</p>\n           </div>\n         ) : (\n           <div className=\"videos-container\">\n             {videos.map(video => (\n               <PublicVideoCard\n                 key={video.id}\n                 video={video}\n                 onPlay={setCurrentVideo}\n                 onShowAuth={onShowAuth}\n               />\n             ))}\n           </div>\n         )}\n       </section>\n\n       {/* Video Player Modal */}\n       {currentVideo && (\n         <PublicVideoPlayer\n           videoId={currentVideo}\n           onClose={() => setCurrentVideo(null)}\n           onShowAuth={onShowAuth}\n         />\n       )}\n    </div>\n  );\n};\n\n// Auth Callback Component (for any remaining callback scenarios)\nconst AuthCallback = () => {\n  const { user } = useAuth();\n  \n  useEffect(() => {\n    // If user is already authenticated, redirect to home\n    if (user) {\n      window.location.href = '/';\n    } else {\n      // If no user and no valid callback, redirect to home\n      setTimeout(() => {\n        window.location.href = '/';\n      }, 2000);\n    }\n  }, [user]);\n\n  return (\n    <div className=\"loading\">\n      <p>Redirecting...</p>\n    </div>\n  );\n};\n\n// Main App Router\nconst App = () => {\n  return (\n    <AuthProvider>\n      <AppRouter />\n    </AuthProvider>\n  );\n};\n\nconst AppRouter = () => {\n  const { user, loading } = useAuth();\n  const [showAuthModal, setShowAuthModal] = useState(false);\n\n  if (loading) {\n    return <div className=\"loading\">Loading...</div>;\n  }\n\n  // Handle auth callback\n  if (window.location.pathname === '/auth/callback') {\n    return <AuthCallback />;\n  }\n\n  // If user is not logged in, show main app with limited functionality\n  if (!user) {\n    return (\n      <>\n        <PublicApp onShowAuth={() => setShowAuthModal(true)} />\n        {showAuthModal && (\n          <div className=\"modal-overlay\">\n            <div className=\"modal-content auth-modal\">\n              <button \n                className=\"modal-close\" \n                onClick={() => setShowAuthModal(false)}\n              >\n                ×\n              </button>\n              <AuthForms />\n            </div>\n          </div>\n        )}\n      </>\n    );\n  }\n\n  if (!user.age_verified) {\n    return (\n      <div className=\"error-container\">\n        <h2>Age Verification Required</h2>\n        <p>You must verify your age to access this content.</p>\n      </div>\n    );\n  }\n\n  return <MainApp />;\n};\n\nexport default App;"], "mappings": ";;;;;;;;;;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,EAAEC,UAAU,QAAQ,OAAO;AAC7E,OAAO,WAAW;AAClB,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB;AACrD,MAAMC,GAAG,GAAG,GAAGJ,WAAW,MAAM;;AAEhC;AACA,MAAMK,WAAW,gBAAGZ,aAAa,CAAC,CAAC;AAEnC,MAAMa,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAACsB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;EACjE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAE9DC,SAAS,CAAC,MAAM;IACd,IAAImB,KAAK,EAAE;MACThB,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUV,KAAK,EAAE;MAClEW,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACLN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACL,KAAK,CAAC,CAAC;EAEX,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,MAAMC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAE9D,MAAMC,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAAC,GAAGzB,GAAG,eAAe,EAAE;QACtD0B,MAAM,EAAEP,UAAU,CAACO,MAAM;QACzBC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFC,YAAY,CAACP,SAAS,CAAC;MACvBf,OAAO,CAACkB,QAAQ,CAACK,IAAI,CAAC;MACtBf,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;;MAEhD;MACA,IAAIA,KAAK,CAACE,IAAI,KAAK,cAAc,IAAIF,KAAK,CAACG,IAAI,KAAK,YAAY,IAAI,CAACH,KAAK,CAACN,QAAQ,EAAE;QACnFV,mBAAmB,CAAC,KAAK,CAAC;QAC1B;QACAiB,OAAO,CAACG,IAAI,CAAC,+DAA+D,CAAC;MAC/E,CAAC,MAAM;QACL;QACAC,MAAM,CAAC,CAAC;MACV;IACF,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,KAAK,GAAGA,CAAC7B,KAAK,EAAE8B,QAAQ,KAAK;IACjC5B,YAAY,CAAC6B,OAAO,CAAC,OAAO,EAAE/B,KAAK,CAAC;IACpCC,QAAQ,CAACD,KAAK,CAAC;IACfD,OAAO,CAAC+B,QAAQ,CAAC;IACjB9C,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUV,KAAK,EAAE;EACpE,CAAC;EAED,MAAM4B,MAAM,GAAGA,CAAA,KAAM;IACnB1B,YAAY,CAAC8B,UAAU,CAAC,OAAO,CAAC;IAChC/B,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;IACb,OAAOf,KAAK,CAACwB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;EACvD,CAAC;EAED,oBACExB,OAAA,CAACQ,WAAW,CAACuC,QAAQ;IAACC,KAAK,EAAE;MAAEpC,IAAI;MAAE+B,KAAK;MAAED,MAAM;MAAExB,OAAO;MAAEE;IAAiB,CAAE;IAAAV,QAAA,EAC7EA;EAAQ;IAAAuC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACzC,EAAA,CAjEIF,YAAY;AAAA4C,EAAA,GAAZ5C,YAAY;AAmElB,MAAM6C,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpB,MAAMC,OAAO,GAAG3D,UAAU,CAACW,WAAW,CAAC;EACvC,IAAI,CAACgD,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,GAAA,CARMD,OAAO;AASb,MAAMI,oBAAoB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAS,CAAC,KAAK;EAC/D,IAAI,CAACF,MAAM,EAAE,OAAO,IAAI;EAExB,oBACE3D,OAAA;IAAK8D,SAAS,EAAC,eAAe;IAAApD,QAAA,eAC5BV,OAAA;MAAK8D,SAAS,EAAC,eAAe;MAAApD,QAAA,gBAC5BV,OAAA;QAAAU,QAAA,EAAI;MAAyB;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClCpD,OAAA;QAAAU,QAAA,EAAG;MAA+C;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACtDpD,OAAA;QAAAU,QAAA,EAAG;MAAoF;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC3FpD,OAAA;QAAK8D,SAAS,EAAC,eAAe;QAAApD,QAAA,gBAC5BV,OAAA;UAAQ+D,OAAO,EAAEH,QAAS;UAACE,SAAS,EAAC,iBAAiB;UAAApD,QAAA,EAAC;QAAQ;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxEpD,OAAA;UAAQ+D,OAAO,EAAEF,QAAS;UAACC,SAAS,EAAC,mBAAmB;UAAApD,QAAA,EAAC;QAAM;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAY,GAAA,GAlBMN,oBAAoB;AAmB1B,MAAMO,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC;IACvC6E,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZhC,IAAI,EAAE,EAAE;IACRiC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC2C,KAAK,EAAEuC,QAAQ,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM;IAAEiD;EAAM,CAAC,GAAGW,OAAO,CAAC,CAAC;EAE3B,MAAMuB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBH,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI,CAACT,OAAO,IAAI,CAACE,QAAQ,CAACI,YAAY,EAAE;MACtCE,sBAAsB,CAAC,IAAI,CAAC;MAC5B;IACF;IAEA,IAAI;MACF,MAAMK,QAAQ,GAAGb,OAAO,GAAG,aAAa,GAAG,gBAAgB;MAC3D,MAAMpC,QAAQ,GAAG,MAAMjC,KAAK,CAACmF,IAAI,CAAC,GAAG1E,GAAG,GAAGyE,QAAQ,EAAE,EAAEX,QAAQ,CAAC;MAEhE1B,KAAK,CAACZ,QAAQ,CAACK,IAAI,CAAC8C,YAAY,EAAEnD,QAAQ,CAACK,IAAI,CAACxB,IAAI,CAAC;IACvD,CAAC,CAAC,OAAOyB,KAAK,EAAE;MAAA,IAAA8C,eAAA,EAAAC,oBAAA;MACd;MACA,IAAIC,YAAY,GAAG,uBAAuB;MAC1C,KAAAF,eAAA,GAAI9C,KAAK,CAACN,QAAQ,cAAAoD,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgB/C,IAAI,cAAAgD,oBAAA,eAApBA,oBAAA,CAAsBE,MAAM,EAAE;QAChC,IAAIC,KAAK,CAACC,OAAO,CAACnD,KAAK,CAACN,QAAQ,CAACK,IAAI,CAACkD,MAAM,CAAC,EAAE;UAC7C;UACAD,YAAY,GAAGhD,KAAK,CAACN,QAAQ,CAACK,IAAI,CAACkD,MAAM,CAACG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAC1E,CAAC,MAAM,IAAI,OAAOvD,KAAK,CAACN,QAAQ,CAACK,IAAI,CAACkD,MAAM,KAAK,QAAQ,EAAE;UACzDD,YAAY,GAAGhD,KAAK,CAACN,QAAQ,CAACK,IAAI,CAACkD,MAAM;QAC3C,CAAC,MAAM;UACLD,YAAY,GAAGQ,IAAI,CAACC,SAAS,CAACzD,KAAK,CAACN,QAAQ,CAACK,IAAI,CAACkD,MAAM,CAAC;QAC3D;MACF;MACAV,QAAQ,CAACS,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMU,qBAAqB,GAAIC,QAAQ,IAAK;IAC1CrB,sBAAsB,CAAC,KAAK,CAAC;IAC7B,IAAIqB,QAAQ,EAAE;MACZ1B,WAAW,CAAC;QAAE,GAAGD,QAAQ;QAAEI,YAAY,EAAE;MAAK,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMwB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAGC,MAAM,CAACC,IAAI,CACvB,GAAG7F,GAAG,oBAAoB,EAC1B,cAAc,EACd,mDACF,CAAC;IAED,MAAM8F,aAAa,GAAIC,KAAK,IAAK;MAC/B;MACA,IAAIA,KAAK,CAACC,MAAM,KAAKJ,MAAM,CAACK,QAAQ,CAACD,MAAM,IAAID,KAAK,CAACC,MAAM,KAAKpG,WAAW,EAAE;MAE7E,IAAImG,KAAK,CAAClE,IAAI,CAACtB,KAAK,IAAIwF,KAAK,CAAClE,IAAI,CAACxB,IAAI,EAAE;QACvC+B,KAAK,CAAC2D,KAAK,CAAClE,IAAI,CAACtB,KAAK,EAAEwF,KAAK,CAAClE,IAAI,CAACxB,IAAI,CAAC;QACxC,IAAIsF,KAAK,IAAI,CAACA,KAAK,CAACO,MAAM,EAAE;UAC1BP,KAAK,CAACQ,KAAK,CAAC,CAAC;QACf;QACAP,MAAM,CAACQ,mBAAmB,CAAC,SAAS,EAAEN,aAAa,CAAC;MACtD;IACF,CAAC;IAEDF,MAAM,CAACS,gBAAgB,CAAC,SAAS,EAAEP,aAAa,CAAC;;IAEjD;IACA,MAAMQ,WAAW,GAAGC,WAAW,CAAC,MAAM;MACpC,IAAIZ,KAAK,IAAIA,KAAK,CAACO,MAAM,EAAE;QACzBM,aAAa,CAACF,WAAW,CAAC;QAC1BV,MAAM,CAACQ,mBAAmB,CAAC,SAAS,EAAEN,aAAa,CAAC;MACtD;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACErG,OAAA;IAAK8D,SAAS,EAAC,gBAAgB;IAAApD,QAAA,gBAC7BV,OAAA;MAAK8D,SAAS,EAAC,WAAW;MAAApD,QAAA,gBACxBV,OAAA;QAAAU,QAAA,EAAKyD,OAAO,GAAG,SAAS,GAAG;MAAgB;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAEhDf,KAAK,iBAAIrC,OAAA;QAAK8D,SAAS,EAAC,eAAe;QAAApD,QAAA,EAAE2B;MAAK;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEtDpD,OAAA;QAAMgH,QAAQ,EAAEnC,YAAa;QAAAnE,QAAA,GAC1B,CAACyD,OAAO,iBACPnE,OAAA;UACEiH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,WAAW;UACvBlE,KAAK,EAAEqB,QAAQ,CAAC7B,IAAK;UACrB2E,QAAQ,EAAGrC,CAAC,IAAKR,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAE7B,IAAI,EAAEsC,CAAC,CAACsC,MAAM,CAACpE;UAAM,CAAC,CAAE;UACpEqE,QAAQ;QAAA;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACF,eACDpD,OAAA;UACEiH,IAAI,EAAC,OAAO;UACZC,WAAW,EAAC,OAAO;UACnBlE,KAAK,EAAEqB,QAAQ,CAACE,KAAM;UACtB4C,QAAQ,EAAGrC,CAAC,IAAKR,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAEE,KAAK,EAAEO,CAAC,CAACsC,MAAM,CAACpE;UAAM,CAAC,CAAE;UACrEqE,QAAQ;QAAA;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFpD,OAAA;UACEiH,IAAI,EAAC,UAAU;UACfC,WAAW,EAAC,UAAU;UACtBlE,KAAK,EAAEqB,QAAQ,CAACG,QAAS;UACzB2C,QAAQ,EAAGrC,CAAC,IAAKR,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAEG,QAAQ,EAAEM,CAAC,CAACsC,MAAM,CAACpE;UAAM,CAAC,CAAE;UACxEqE,QAAQ;QAAA;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFpD,OAAA;UAAQiH,IAAI,EAAC,QAAQ;UAACnD,SAAS,EAAC,iBAAiB;UAAApD,QAAA,EAC9CyD,OAAO,GAAG,SAAS,GAAG;QAAgB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPpD,OAAA;QAAK8D,SAAS,EAAC,cAAc;QAAApD,QAAA,eAC3BV,OAAA;UAAAU,QAAA,EAAM;QAAE;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAENpD,OAAA;QAAQ+D,OAAO,EAAEkC,iBAAkB;QAACnC,SAAS,EAAC,gBAAgB;QAAApD,QAAA,EAAC;MAE/D;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETpD,OAAA;QAAG8D,SAAS,EAAC,aAAa;QAAApD,QAAA,GACvByD,OAAO,GAAG,wBAAwB,GAAG,0BAA0B,eAChEnE,OAAA;UAAQ+D,OAAO,EAAEA,CAAA,KAAMK,UAAU,CAAC,CAACD,OAAO,CAAE;UAACL,SAAS,EAAC,aAAa;UAAApD,QAAA,EACjEyD,OAAO,GAAG,SAAS,GAAG;QAAS;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENpD,OAAA,CAAC0D,oBAAoB;MACnBC,MAAM,EAAEe,mBAAoB;MAC5Bd,QAAQ,EAAEA,CAAA,KAAMmC,qBAAqB,CAAC,IAAI,CAAE;MAC5ClC,QAAQ,EAAEA,CAAA,KAAMkC,qBAAqB,CAAC,KAAK;IAAE;MAAA9C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAc,GAAA,CA9IMD,SAAS;EAAA,QAUKX,OAAO;AAAA;AAAAgE,GAAA,GAVrBrD,SAAS;AA+If,MAAMsD,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMmB,KAAK,GAAGE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAM4G,GAAG,GAAG,GAAGtH,GAAG,WAAWiH,OAAO,iBAAiBM,kBAAkB,CAAChH,KAAK,CAAC,EAAE;IAChF8G,WAAW,CAACC,GAAG,CAAC;IAChB1G,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,CAACqG,OAAO,CAAC,CAAC;EAEb,oBACExH,OAAA;IAAK8D,SAAS,EAAC,oBAAoB;IAAApD,QAAA,eACjCV,OAAA;MAAK8D,SAAS,EAAC,sBAAsB;MAAApD,QAAA,gBACnCV,OAAA;QAAQ8D,SAAS,EAAC,cAAc;QAACC,OAAO,EAAE0D,OAAQ;QAAA/G,QAAA,EAAC;MAAC;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAC5DlC,OAAO,gBACNlB,OAAA;QAAK8D,SAAS,EAAC,SAAS;QAAApD,QAAA,EAAC;MAAgB;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAE/CpD,OAAA;QAAO+H,QAAQ;QAACC,QAAQ;QAAClE,SAAS,EAAC,cAAc;QAAApD,QAAA,gBAC/CV,OAAA;UAAQiI,GAAG,EAAEN,QAAS;UAACV,IAAI,EAAC;QAAW;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gDAE5C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAsE,GAAA,CA5BMH,WAAW;AAAAW,GAAA,GAAXX,WAAW;AA6BjB,MAAMY,SAAS,GAAGA,CAAC;EAAEC,KAAK;EAAEC,MAAM;EAAEC,SAAS;EAAEC,QAAQ;EAAEC;AAAQ,CAAC,KAAK;EACrE,MAAMC,eAAe,GAAI3D,CAAC,IAAK;IAC7B;IACA,IAAIA,CAAC,CAACsC,MAAM,CAACsB,OAAO,CAAC,gBAAgB,CAAC,EAAE;MACtC;IACF;IACAL,MAAM,CAACD,KAAK,CAACO,EAAE,CAAC;EAClB,CAAC;EAED,MAAMC,YAAY,GAAGR,KAAK,CAACS,SAAS,GAChC,GAAGtI,GAAG,WAAW6H,KAAK,CAACO,EAAE,YAAY,GACrC,mFAAmF;EAEvF,oBACE3I,OAAA;IAAK8D,SAAS,EAAC,YAAY;IAACC,OAAO,EAAE0E,eAAgB;IAAA/H,QAAA,gBACnDV,OAAA;MAAK8D,SAAS,EAAC,iBAAiB;MAAApD,QAAA,gBAC9BV,OAAA;QACEiI,GAAG,EAAEW,YAAa;QAClBE,GAAG,EAAEV,KAAK,CAACW;MAAM;QAAA9F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACFpD,OAAA;QAAK8D,SAAS,EAAC,cAAc;QAAApD,QAAA,eAC3BV,OAAA;UAAK8D,SAAS,EAAC,aAAa;UAAApD,QAAA,EAAC;QAAC;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,EACLgF,KAAK,CAACY,MAAM,KAAK,SAAS,iBACzBhJ,OAAA;QAAK8D,SAAS,EAAC,sBAAsB;QAAApD,QAAA,EAAC;MAAO;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACnD,EACAgF,KAAK,CAACY,MAAM,KAAK,UAAU,iBAC1BhJ,OAAA;QAAK8D,SAAS,EAAC,uBAAuB;QAAApD,QAAA,EAAC;MAAQ;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACrD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACNpD,OAAA;MAAK8D,SAAS,EAAC,YAAY;MAAApD,QAAA,gBACzBV,OAAA;QAAAU,QAAA,EAAK0H,KAAK,CAACW;MAAK;QAAA9F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtBpD,OAAA;QAAAU,QAAA,EAAI0H,KAAK,CAACa;MAAW;QAAAhG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BpD,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAApD,QAAA,gBACzBV,OAAA;UAAM8D,SAAS,EAAC,UAAU;UAAApD,QAAA,EAAE0H,KAAK,CAACc;QAAQ;UAAAjG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClDpD,OAAA;UAAM8D,SAAS,EAAC,OAAO;UAAApD,QAAA,GAAE0H,KAAK,CAACe,KAAK,EAAC,QAAM;QAAA;UAAAlG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNpD,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAApD,QAAA,EACxB0H,KAAK,CAACgB,IAAI,CAAC3D,GAAG,CAAC4D,GAAG,iBACjBrJ,OAAA;UAAgB8D,SAAS,EAAC,KAAK;UAAApD,QAAA,EAAE2I;QAAG,GAAzBA,GAAG;UAAApG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA6B,CAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EACLoF,OAAO,IAAIJ,KAAK,CAACY,MAAM,KAAK,SAAS,iBACpChJ,OAAA;QAAK8D,SAAS,EAAC,eAAe;QAAApD,QAAA,gBAC5BV,OAAA;UACE+D,OAAO,EAAEA,CAAA,KAAMuE,SAAS,CAACF,KAAK,CAACO,EAAE,CAAE;UACnC7E,SAAS,EAAC,wBAAwB;UAAApD,QAAA,EACnC;QAED;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpD,OAAA;UACE+D,OAAO,EAAEA,CAAA,KAAMwE,QAAQ,CAACH,KAAK,CAACO,EAAE,CAAE;UAClC7E,SAAS,EAAC,uBAAuB;UAAApD,QAAA,EAClC;QAED;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAkG,GAAA,GA/DMnB,SAAS;AAgEf,MAAMoB,SAAS,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAa,CAAC,KAAK;EAAAC,GAAA;EAC9C,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlK,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmK,YAAY,EAAEC,eAAe,CAAC,GAAGpK,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqK,KAAK,EAAEC,QAAQ,CAAC,GAAGtK,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuK,SAAS,EAAEC,YAAY,CAAC,GAAGxK,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2C,KAAK,EAAEuC,QAAQ,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd,IAAIsK,SAAS,KAAK,QAAQ,EAAE;MAC1BE,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM,IAAIF,SAAS,KAAK,OAAO,EAAE;MAChCG,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACH,SAAS,CAAC,CAAC;EAEf,MAAME,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMpI,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAAC,GAAGzB,GAAG,SAAS,CAAC;MACjDqJ,cAAc,CAAC7H,QAAQ,CAACK,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CuC,QAAQ,CAAC,uBAAuB,CAAC;IACnC,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiJ,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMtJ,KAAK,GAAGE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMc,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAAC,GAAGzB,GAAG,cAAc,EAAE;QACrDgB,OAAO,EAAE;UAAE8I,aAAa,EAAE,UAAUvJ,KAAK;QAAG;MAC9C,CAAC,CAAC;MACFkJ,QAAQ,CAACjI,QAAQ,CAACK,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CuC,QAAQ,CAAC,sBAAsB,CAAC;IAClC,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmJ,eAAe,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI,CAACpE,MAAM,CAACqE,OAAO,CAAC,2DAA2D,CAAC,EAAE;MAChF;IACF;IAEA,IAAI;MACF,MAAM1J,KAAK,GAAGE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMnB,KAAK,CAACmF,IAAI,CAAC,GAAG1E,GAAG,gBAAgBgK,MAAM,aAAa,EAAE,CAAC,CAAC,EAAE;QAC9DhJ,OAAO,EAAE;UAAE8I,aAAa,EAAE,UAAUvJ,KAAK;QAAG;MAC9C,CAAC,CAAC;MACFsJ,UAAU,CAAC,CAAC,CAAC,CAAC;MACdK,KAAK,CAAC,wCAAwC,CAAC;IACjD,CAAC,CAAC,OAAOpI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDoI,KAAK,CAAC,8BAA8B,CAAC;IACvC;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAOH,MAAM,IAAK;IAC1C,IAAI;MACF,MAAMzJ,KAAK,GAAGE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMnB,KAAK,CAACmF,IAAI,CAAC,GAAG1E,GAAG,gBAAgBgK,MAAM,UAAU,EAAE,CAAC,CAAC,EAAE;QAC3DhJ,OAAO,EAAE;UAAE8I,aAAa,EAAE,UAAUvJ,KAAK;QAAG;MAC9C,CAAC,CAAC;MACFsJ,UAAU,CAAC,CAAC,CAAC,CAAC;MACdK,KAAK,CAAC,4BAA4B,CAAC;IACrC,CAAC,CAAC,OAAOpI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CoI,KAAK,CAAC,wBAAwB,CAAC;IACjC;EACF,CAAC;EAED,MAAME,eAAe,GAAIvC,KAAK,IAAK;IACjC0B,eAAe,CAAC1B,KAAK,CAAC;EACxB,CAAC;EAED,MAAMwC,iBAAiB,GAAG,MAAAA,CAAOpD,OAAO,EAAEqD,UAAU,KAAK;IACvD,IAAI;MACF,MAAM/K,KAAK,CAACgL,GAAG,CAAC,GAAGvK,GAAG,WAAWiH,OAAO,EAAE,EAAEqD,UAAU,CAAC;MACvDf,eAAe,CAAC,IAAI,CAAC;MACrBK,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAO9H,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CoI,KAAK,CAAC,wBAAwB,CAAC;IACjC;EACF,CAAC;EAED,MAAMM,iBAAiB,GAAG,MAAOvD,OAAO,IAAK;IAC3C,IAAI,CAACrB,MAAM,CAACqE,OAAO,CAAC,2EAA2E,CAAC,EAAE;MAChG;IACF;IAEA,IAAI;MACF,MAAM1K,KAAK,CAACkL,MAAM,CAAC,GAAGzK,GAAG,WAAWiH,OAAO,EAAE,CAAC;MAC9C2C,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAO9H,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CoI,KAAK,CAAC,wBAAwB,CAAC;IACjC;EACF,CAAC;EAED,IAAIvJ,OAAO,EAAE;IACX,oBAAOlB,OAAA;MAAK8D,SAAS,EAAC,SAAS;MAAApD,QAAA,EAAC;IAAsB;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC9D;EAEA,oBACIpD,OAAA;IAAK8D,SAAS,EAAC,YAAY;IAAApD,QAAA,gBACzBV,OAAA;MAAK8D,SAAS,EAAC,cAAc;MAAApD,QAAA,gBAC3BV,OAAA;QAAAU,QAAA,EAAI;MAAW;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpBpD,OAAA;QAAK8D,SAAS,EAAC,sBAAsB;QAAApD,QAAA,GAClCuJ,SAAS,KAAK,QAAQ,iBACrBjK,OAAA;UAAQ+D,OAAO,EAAE0F,YAAa;UAAC3F,SAAS,EAAC,iBAAiB;UAAApD,QAAA,EAAC;QAE3D;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eACDpD,OAAA;UAAQ+D,OAAO,EAAEyF,MAAO;UAAC1F,SAAS,EAAC,mBAAmB;UAAApD,QAAA,EAAC;QAAY;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpD,OAAA;MAAK8D,SAAS,EAAC,YAAY;MAAApD,QAAA,gBACzBV,OAAA;QACE8D,SAAS,EAAE,cAAcmG,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;QAClElG,OAAO,EAAEA,CAAA,KAAMmG,YAAY,CAAC,QAAQ,CAAE;QAAAxJ,QAAA,EACvC;MAED;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTpD,OAAA;QACE8D,SAAS,EAAE,cAAcmG,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;QACjElG,OAAO,EAAEA,CAAA,KAAMmG,YAAY,CAAC,OAAO,CAAE;QAAAxJ,QAAA,EACtC;MAED;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELf,KAAK,iBAAIrC,OAAA;MAAK8D,SAAS,EAAC,eAAe;MAAApD,QAAA,EAAE2B;IAAK;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAErD6G,SAAS,KAAK,QAAQ,gBACrBjK,OAAA;MAAK8D,SAAS,EAAC,mBAAmB;MAAApD,QAAA,EAC/BiJ,WAAW,CAACsB,MAAM,KAAK,CAAC,gBACvBjL,OAAA;QAAK8D,SAAS,EAAC,WAAW;QAAApD,QAAA,eACxBV,OAAA;UAAAU,QAAA,EAAI;QAAe;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,GAENuG,WAAW,CAAClE,GAAG,CAAC2C,KAAK,iBACnBpI,OAAA,CAACkL,cAAc;QAEb9C,KAAK,EAAEA,KAAM;QACb+C,MAAM,EAAER,eAAgB;QACxBS,QAAQ,EAAEL;MAAkB,GAHvB3C,KAAK,CAACO,EAAE;QAAA1F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAId,CACF;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,gBAENpD,OAAA;MAAK8D,SAAS,EAAC,qBAAqB;MAAApD,QAAA,gBAClCV,OAAA;QAAAU,QAAA,EAAI;MAAS;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClBpD,OAAA;QAAK8D,SAAS,EAAC,aAAa;QAAApD,QAAA,EACzBqJ,KAAK,CAACkB,MAAM,KAAK,CAAC,gBACjBjL,OAAA;UAAK8D,SAAS,EAAC,UAAU;UAAApD,QAAA,eACvBV,OAAA;YAAAU,QAAA,EAAG;UAAc;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,gBAENpD,OAAA;UAAO8D,SAAS,EAAC,aAAa;UAAApD,QAAA,gBAC5BV,OAAA;YAAAU,QAAA,eACEV,OAAA;cAAAU,QAAA,gBACEV,OAAA;gBAAAU,QAAA,EAAI;cAAI;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbpD,OAAA;gBAAAU,QAAA,EAAI;cAAK;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdpD,OAAA;gBAAAU,QAAA,EAAI;cAAK;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdpD,OAAA;gBAAAU,QAAA,EAAI;cAAQ;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjBpD,OAAA;gBAAAU,QAAA,EAAI;cAAO;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRpD,OAAA;YAAAU,QAAA,EACGqJ,KAAK,CAACtE,GAAG,CAAC7E,IAAI,iBACbZ,OAAA;cAAAU,QAAA,gBACEV,OAAA;gBAAAU,QAAA,EAAKE,IAAI,CAAC4B;cAAI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpBpD,OAAA;gBAAAU,QAAA,EAAKE,IAAI,CAAC2D;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrBpD,OAAA;gBAAAU,QAAA,EAAKE,IAAI,CAACyK,QAAQ,GAAG,KAAK,GAAG;cAAI;gBAAApI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCpD,OAAA;gBAAAU,QAAA,EAAKE,IAAI,CAAC0K,WAAW,GAAG,KAAK,GAAG;cAAI;gBAAArI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1CpD,OAAA;gBAAAU,QAAA,GACG,CAACE,IAAI,CAACyK,QAAQ,iBACbrL,OAAA;kBACE+D,OAAO,EAAEA,CAAA,KAAMuG,eAAe,CAAC1J,IAAI,CAAC+H,EAAE,CAAE;kBACxC7E,SAAS,EAAC,wBAAwB;kBAAApD,QAAA,EACnC;gBAED;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,EACA,CAACxC,IAAI,CAAC0K,WAAW,iBAChBtL,OAAA;kBACE+D,OAAO,EAAEA,CAAA,KAAM2G,iBAAiB,CAAC9J,IAAI,CAAC+H,EAAE,CAAE;kBAC1C7E,SAAS,EAAC,wBAAwB;kBAClCyH,KAAK,EAAE;oBAACC,UAAU,EAAE;kBAAK,CAAE;kBAAA9K,QAAA,EAC5B;gBAED;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GAvBExC,IAAI,CAAC+H,EAAE;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEAyG,YAAY,iBACX7J,OAAA,CAACyL,cAAc;MACbrD,KAAK,EAAEyB,YAAa;MACpBpC,OAAO,EAAEA,CAAA,KAAMqC,eAAe,CAAC,IAAI,CAAE;MACrC4B,QAAQ,EAAEd;IAAkB;MAAA3H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEZ,CAAC;;AAED;AAAAsG,GAAA,CA7NMH,SAAS;AAAAoC,GAAA,GAATpC,SAAS;AA8Nf,MAAM2B,cAAc,GAAGA,CAAC;EAAE9C,KAAK;EAAE+C,MAAM;EAAEC;AAAS,CAAC,KAAK;EACtD,MAAMxC,YAAY,GAAGR,KAAK,CAACS,SAAS,GAChC,GAAGtI,GAAG,WAAW6H,KAAK,CAACO,EAAE,YAAY,GACrC,mFAAmF;EAEvF,oBACE3I,OAAA;IAAK8D,SAAS,EAAC,kBAAkB;IAAApD,QAAA,gBAC/BV,OAAA;MAAK8D,SAAS,EAAC,iBAAiB;MAAApD,QAAA,gBAC9BV,OAAA;QACEiI,GAAG,EAAEW,YAAa;QAClBE,GAAG,EAAEV,KAAK,CAACW;MAAM;QAAA9F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACFpD,OAAA;QAAK8D,SAAS,EAAE,gBAAgBsE,KAAK,CAACY,MAAM,EAAG;QAAAtI,QAAA,EAAE0H,KAAK,CAACY;MAAM;QAAA/F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eACNpD,OAAA;MAAK8D,SAAS,EAAC,YAAY;MAAApD,QAAA,gBACzBV,OAAA;QAAAU,QAAA,EAAK0H,KAAK,CAACW;MAAK;QAAA9F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACtBpD,OAAA;QAAAU,QAAA,EAAI0H,KAAK,CAACa;MAAW;QAAAhG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BpD,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAApD,QAAA,gBACzBV,OAAA;UAAM8D,SAAS,EAAC,UAAU;UAAApD,QAAA,EAAE0H,KAAK,CAACc;QAAQ;UAAAjG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAClDpD,OAAA;UAAM8D,SAAS,EAAC,OAAO;UAAApD,QAAA,GAAE0H,KAAK,CAACe,KAAK,EAAC,QAAM;QAAA;UAAAlG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNpD,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAApD,QAAA,EACxB0H,KAAK,CAACgB,IAAI,CAAC3D,GAAG,CAAC4D,GAAG,iBACjBrJ,OAAA;UAAgB8D,SAAS,EAAC,KAAK;UAAApD,QAAA,EAAE2I;QAAG,GAAzBA,GAAG;UAAApG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA6B,CAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNpD,OAAA;QAAK8D,SAAS,EAAC,eAAe;QAAApD,QAAA,gBAC5BV,OAAA;UACE+D,OAAO,EAAEA,CAAA,KAAMoH,MAAM,CAAC/C,KAAK,CAAE;UAC7BtE,SAAS,EAAC,wBAAwB;UAAApD,QAAA,EACnC;QAED;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpD,OAAA;UACE+D,OAAO,EAAEA,CAAA,KAAMqH,QAAQ,CAAChD,KAAK,CAACO,EAAE,CAAE;UAClC7E,SAAS,EAAC,uBAAuB;UAAApD,QAAA,EAClC;QAED;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAwI,GAAA,GA7CMV,cAAc;AA8CpB,MAAMO,cAAc,GAAGA,CAAC;EAAErD,KAAK;EAAEX,OAAO;EAAEiE;AAAS,CAAC,KAAK;EAAAG,GAAA;EACvD,MAAM,CAACxH,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC;IACvCqJ,KAAK,EAAEX,KAAK,CAACW,KAAK;IAClBE,WAAW,EAAEb,KAAK,CAACa,WAAW;IAC9BC,QAAQ,EAAEd,KAAK,CAACc,QAAQ;IACxBE,IAAI,EAAEhB,KAAK,CAACgB,IAAI,CAACxD,IAAI,CAAC,IAAI;EAC5B,CAAC,CAAC;EACF,MAAM,CAACkG,MAAM,EAAEC,SAAS,CAAC,GAAGrM,QAAQ,CAAC,KAAK,CAAC;EAE3C,MAAMmF,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBgH,SAAS,CAAC,IAAI,CAAC;IAEf,MAAMlB,UAAU,GAAG;MACjB,GAAGxG,QAAQ;MACX+E,IAAI,EAAE/E,QAAQ,CAAC+E,IAAI,CAAC4C,KAAK,CAAC,GAAG,CAAC,CAACvG,GAAG,CAAC4D,GAAG,IAAIA,GAAG,CAAC4C,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC7C,GAAG,IAAIA,GAAG;IACzE,CAAC;IAED,MAAMqC,QAAQ,CAACtD,KAAK,CAACO,EAAE,EAAEkC,UAAU,CAAC;IACpCkB,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,oBACE/L,OAAA;IAAK8D,SAAS,EAAC,eAAe;IAAApD,QAAA,eAC5BV,OAAA;MAAK8D,SAAS,EAAC,gCAAgC;MAAApD,QAAA,gBAC7CV,OAAA;QAAAU,QAAA,EAAI;MAAU;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEnBpD,OAAA;QAAMgH,QAAQ,EAAEnC,YAAa;QAAAnE,QAAA,gBAC3BV,OAAA;UACEiH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,aAAa;UACzBlE,KAAK,EAAEqB,QAAQ,CAAC0E,KAAM;UACtB5B,QAAQ,EAAGrC,CAAC,IAAKR,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAE0E,KAAK,EAAEjE,CAAC,CAACsC,MAAM,CAACpE;UAAM,CAAC,CAAE;UACrEqE,QAAQ;QAAA;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFpD,OAAA;UACEkH,WAAW,EAAC,mBAAmB;UAC/BlE,KAAK,EAAEqB,QAAQ,CAAC4E,WAAY;UAC5B9B,QAAQ,EAAGrC,CAAC,IAAKR,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAE4E,WAAW,EAAEnE,CAAC,CAACsC,MAAM,CAACpE;UAAM,CAAC,CAAE;UAC3EmJ,IAAI,EAAC,GAAG;UACR9E,QAAQ;QAAA;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFpD,OAAA;UACEiH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,UAAU;UACtBlE,KAAK,EAAEqB,QAAQ,CAAC6E,QAAS;UACzB/B,QAAQ,EAAGrC,CAAC,IAAKR,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAE6E,QAAQ,EAAEpE,CAAC,CAACsC,MAAM,CAACpE;UAAM,CAAC,CAAE;UACxEqE,QAAQ;QAAA;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFpD,OAAA;UACEiH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,wBAAwB;UACpClE,KAAK,EAAEqB,QAAQ,CAAC+E,IAAK;UACrBjC,QAAQ,EAAGrC,CAAC,IAAKR,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAE+E,IAAI,EAAEtE,CAAC,CAACsC,MAAM,CAACpE;UAAM,CAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eAEFpD,OAAA;UAAK8D,SAAS,EAAC,eAAe;UAAApD,QAAA,gBAC5BV,OAAA;YAAQiH,IAAI,EAAC,QAAQ;YAACnD,SAAS,EAAC,iBAAiB;YAACsI,QAAQ,EAAEN,MAAO;YAAApL,QAAA,EAChEoL,MAAM,GAAG,WAAW,GAAG;UAAc;YAAA7I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACTpD,OAAA;YAAQiH,IAAI,EAAC,QAAQ;YAAClD,OAAO,EAAE0D,OAAQ;YAAC3D,SAAS,EAAC,mBAAmB;YAAApD,QAAA,EAAC;UAEtE;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAyI,GAAA,CAtEMJ,cAAc;AAAAY,GAAA,GAAdZ,cAAc;AAuEpB,MAAMa,UAAU,GAAGA,CAAC;EAAE7E,OAAO;EAAE8E;AAAU,CAAC,KAAK;EAAAC,GAAA;EAC7C,MAAM,CAACnI,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC;IACvCqJ,KAAK,EAAE,EAAE;IACTE,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACqD,IAAI,EAAEC,OAAO,CAAC,GAAGhN,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACmJ,SAAS,EAAE8D,YAAY,CAAC,GAAGjN,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkN,SAAS,EAAEC,YAAY,CAAC,GAAGnN,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2C,KAAK,EAAEuC,QAAQ,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMmF,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC0H,IAAI,EAAE;MACT7H,QAAQ,CAAC,4BAA4B,CAAC;MACtC;IACF;IAEAiI,YAAY,CAAC,IAAI,CAAC;IAClBjI,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMkI,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;IACjCD,UAAU,CAACE,MAAM,CAAC,OAAO,EAAE3I,QAAQ,CAAC0E,KAAK,CAAC;IAC1C+D,UAAU,CAACE,MAAM,CAAC,aAAa,EAAE3I,QAAQ,CAAC4E,WAAW,CAAC;IACtD6D,UAAU,CAACE,MAAM,CAAC,UAAU,EAAE3I,QAAQ,CAAC6E,QAAQ,CAAC;IAChD4D,UAAU,CAACE,MAAM,CAAC,MAAM,EAAE3I,QAAQ,CAAC+E,IAAI,CAAC;IACxC0D,UAAU,CAACE,MAAM,CAAC,MAAM,EAAEP,IAAI,CAAC;IAC/B,IAAI5D,SAAS,EAAE;MACbiE,UAAU,CAACE,MAAM,CAAC,WAAW,EAAEnE,SAAS,CAAC;IAC3C;IAEA,IAAI;MACF,MAAM/I,KAAK,CAACmF,IAAI,CAAC,GAAG1E,GAAG,gBAAgB,EAAEuM,UAAU,EAAE;QACnDvL,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACFgL,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOlK,KAAK,EAAE;MAAA,IAAA4K,gBAAA,EAAAC,qBAAA;MACd;MACA,IAAI7H,YAAY,GAAG,eAAe;MAClC,KAAA4H,gBAAA,GAAI5K,KAAK,CAACN,QAAQ,cAAAkL,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7K,IAAI,cAAA8K,qBAAA,eAApBA,qBAAA,CAAsB5H,MAAM,EAAE;QAChC,IAAIC,KAAK,CAACC,OAAO,CAACnD,KAAK,CAACN,QAAQ,CAACK,IAAI,CAACkD,MAAM,CAAC,EAAE;UAC7C;UACAD,YAAY,GAAGhD,KAAK,CAACN,QAAQ,CAACK,IAAI,CAACkD,MAAM,CAACG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAC1E,CAAC,MAAM,IAAI,OAAOvD,KAAK,CAACN,QAAQ,CAACK,IAAI,CAACkD,MAAM,KAAK,QAAQ,EAAE;UACzDD,YAAY,GAAGhD,KAAK,CAACN,QAAQ,CAACK,IAAI,CAACkD,MAAM;QAC3C,CAAC,MAAM;UACLD,YAAY,GAAGQ,IAAI,CAACC,SAAS,CAACzD,KAAK,CAACN,QAAQ,CAACK,IAAI,CAACkD,MAAM,CAAC;QAC3D;MACF;MACAV,QAAQ,CAACS,YAAY,CAAC;IACxB,CAAC,SAAS;MACRwH,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACE7M,OAAA;IAAK8D,SAAS,EAAC,eAAe;IAAApD,QAAA,eAC5BV,OAAA;MAAK8D,SAAS,EAAC,4BAA4B;MAAApD,QAAA,gBACzCV,OAAA;QAAAU,QAAA,EAAI;MAAY;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACpBf,KAAK,iBAAIrC,OAAA;QAAK8D,SAAS,EAAC,eAAe;QAAApD,QAAA,EAAE2B;MAAK;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEtDpD,OAAA;QAAMgH,QAAQ,EAAEnC,YAAa;QAAAnE,QAAA,gBAC3BV,OAAA;UACEiH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,aAAa;UACzBlE,KAAK,EAAEqB,QAAQ,CAAC0E,KAAM;UACtB5B,QAAQ,EAAGrC,CAAC,IAAKR,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAE0E,KAAK,EAAEjE,CAAC,CAACsC,MAAM,CAACpE;UAAM,CAAC,CAAE;UACrEqE,QAAQ;QAAA;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFpD,OAAA;UACEkH,WAAW,EAAC,mBAAmB;UAC/BlE,KAAK,EAAEqB,QAAQ,CAAC4E,WAAY;UAC5B9B,QAAQ,EAAGrC,CAAC,IAAKR,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAE4E,WAAW,EAAEnE,CAAC,CAACsC,MAAM,CAACpE;UAAM,CAAC,CAAE;UAC3EmJ,IAAI,EAAC,GAAG;UACR9E,QAAQ;QAAA;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFpD,OAAA;UACEiH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,UAAU;UACtBlE,KAAK,EAAEqB,QAAQ,CAAC6E,QAAS;UACzB/B,QAAQ,EAAGrC,CAAC,IAAKR,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAE6E,QAAQ,EAAEpE,CAAC,CAACsC,MAAM,CAACpE;UAAM,CAAC,CAAE;UACxEqE,QAAQ;QAAA;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFpD,OAAA;UACEiH,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC,wBAAwB;UACpClE,KAAK,EAAEqB,QAAQ,CAAC+E,IAAK;UACrBjC,QAAQ,EAAGrC,CAAC,IAAKR,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAE+E,IAAI,EAAEtE,CAAC,CAACsC,MAAM,CAACpE;UAAM,CAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACFpD,OAAA;UACEiH,IAAI,EAAC,MAAM;UACXkG,MAAM,EAAC,SAAS;UAChBhG,QAAQ,EAAGrC,CAAC,IAAK4H,OAAO,CAAC5H,CAAC,CAACsC,MAAM,CAACgG,KAAK,CAAC,CAAC,CAAC,CAAE;UAC5C/F,QAAQ;QAAA;UAAApE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFpD,OAAA;UACEiH,IAAI,EAAC,MAAM;UACXkG,MAAM,EAAC,SAAS;UAChBhG,QAAQ,EAAGrC,CAAC,IAAK6H,YAAY,CAAC7H,CAAC,CAACsC,MAAM,CAACgG,KAAK,CAAC,CAAC,CAAC,CAAE;UACjDlG,WAAW,EAAC;QAAsB;UAAAjE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACFpD,OAAA;UAAOqN,OAAO,EAAC,WAAW;UAACvJ,SAAS,EAAC,YAAY;UAAApD,QAAA,EAAC;QAAoB;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAE9EpD,OAAA;UAAK8D,SAAS,EAAC,eAAe;UAAApD,QAAA,gBAC5BV,OAAA;YAAQiH,IAAI,EAAC,QAAQ;YAACmF,QAAQ,EAAEQ,SAAU;YAAC9I,SAAS,EAAC,iBAAiB;YAAApD,QAAA,EACnEkM,SAAS,GAAG,cAAc,GAAG;UAAc;YAAA3J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACTpD,OAAA;YAAQiH,IAAI,EAAC,QAAQ;YAAClD,OAAO,EAAE0D,OAAQ;YAAC3D,SAAS,EAAC,mBAAmB;YAAApD,QAAA,EAAC;UAEtE;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAoJ,GAAA,CAxHMF,UAAU;AAAAgB,GAAA,GAAVhB,UAAU;AAyHhB,MAAMiB,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpB,MAAM;IAAE5M,IAAI;IAAE8B;EAAO,CAAC,GAAGY,OAAO,CAAC,CAAC;EAClC,MAAM,CAACmK,MAAM,EAAEC,SAAS,CAAC,GAAGhO,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiO,UAAU,EAAEC,aAAa,CAAC,GAAGlO,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmO,YAAY,EAAEC,eAAe,CAAC,GAAGpO,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqO,UAAU,EAAEC,aAAa,CAAC,GAAGtO,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuO,WAAW,EAAEC,cAAc,CAAC,GAAGxO,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1O,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2O,WAAW,EAAEC,cAAc,CAAC,GAAG5O,QAAQ,CAAC,MAAM,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACd4O,WAAW,CAAC,CAAC;IACbC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACL,gBAAgB,CAAC,CAAC;EAEtB,MAAMI,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAME,MAAM,GAAGN,gBAAgB,GAAG;QAAEjF,QAAQ,EAAEiF;MAAiB,CAAC,GAAG,CAAC,CAAC;MACrE,MAAMpM,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAAC,GAAGzB,GAAG,SAAS,EAAE;QAAEkO;MAAO,CAAC,CAAC;MAC7Df,SAAS,CAAC3L,QAAQ,CAACK,IAAI,IAAI,EAAE,CAAC;IAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqN,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMzM,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAAC,GAAGzB,GAAG,aAAa,CAAC;MACrDqN,aAAa,CAAC7L,QAAQ,CAACK,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDuL,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACT,WAAW,CAAChC,IAAI,CAAC,CAAC,EAAE;MACvBsC,WAAW,CAAC,CAAC;MACb;IACF;IAEA,IAAI;MACF,MAAMxM,QAAQ,GAAG,MAAMjC,KAAK,CAACmF,IAAI,CAAC,GAAG1E,GAAG,SAAS,EAAE;QACjDoO,KAAK,EAAEV,WAAW;QAClB/E,QAAQ,EAAEiF,gBAAgB,IAAI;MAChC,CAAC,CAAC;MACFT,SAAS,CAAC3L,QAAQ,CAACK,IAAI,IAAI,EAAE,CAAC;IAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EAED,MAAMuM,mBAAmB,GAAGA,CAAA,KAAM;IAChCZ,aAAa,CAAC,KAAK,CAAC;IACpB,IAAIK,WAAW,KAAK,OAAO,EAAE;MAC3B;MACA;MACAlI,MAAM,CAACK,QAAQ,CAACqI,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,MAAM;MACLN,WAAW,CAAC,CAAC,CAAC,CAAC;IACjB;EACF,CAAC;EAED,MAAMO,kBAAkB,GAAG,MAAOtH,OAAO,IAAK;IAC5C,IAAI;MACF,MAAM1H,KAAK,CAACmF,IAAI,CAAC,GAAG1E,GAAG,WAAWiH,OAAO,UAAU,CAAC;MACpD+G,WAAW,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOlM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDoI,KAAK,CAAC,yBAAyB,CAAC;IAClC;EACF,CAAC;EAED,MAAMsE,iBAAiB,GAAG,MAAOvH,OAAO,IAAK;IAC3C,IAAI;MACF,MAAM1H,KAAK,CAACmF,IAAI,CAAC,GAAG1E,GAAG,WAAWiH,OAAO,SAAS,CAAC;MACnD+G,WAAW,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOlM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CoI,KAAK,CAAC,wBAAwB,CAAC;IACjC;EACF,CAAC;EAED,IAAIvJ,OAAO,EAAE;IACX,oBAAOlB,OAAA;MAAK8D,SAAS,EAAC,SAAS;MAAApD,QAAA,EAAC;IAAU;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAClD;EAEA,oBACEpD,OAAA;IAAK8D,SAAS,EAAC,KAAK;IAAApD,QAAA,gBAElBV,OAAA;MAAQ8D,SAAS,EAAC,QAAQ;MAAApD,QAAA,eACxBV,OAAA;QAAK8D,SAAS,EAAC,gBAAgB;QAAApD,QAAA,gBAC7BV,OAAA;UAAI8D,SAAS,EAAC,MAAM;UAACC,OAAO,EAAEA,CAAA,KAAMuK,cAAc,CAAC,MAAM,CAAE;UAAC/C,KAAK,EAAE;YAACyD,MAAM,EAAE;UAAS,CAAE;UAAAtO,QAAA,EAAC;QAAS;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtGpD,OAAA;UAAK8D,SAAS,EAAC,gBAAgB;UAAApD,QAAA,GAC5B,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyK,QAAQ,kBACbrL,OAAA;YACE+D,OAAO,EAAEA,CAAA,KAAMuK,cAAc,CAAC,OAAO,CAAE;YACvCxK,SAAS,EAAE,OAAOuK,WAAW,KAAK,OAAO,GAAG,aAAa,GAAG,eAAe,EAAG;YAAA3N,QAAA,EAC/E;UAED;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eACDpD,OAAA;YAAK8D,SAAS,EAAC,WAAW;YAAApD,QAAA,gBACxBV,OAAA;cAAAU,QAAA,GAAM,WAAS,EAACE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,IAAI;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClCpD,OAAA;cAAQ+D,OAAO,EAAErB,MAAO;cAACoB,SAAS,EAAC,mBAAmB;cAAApD,QAAA,EAAC;YAAM;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGRiL,WAAW,KAAK,OAAO,gBACtBrO,OAAA,CAACuJ,SAAS;MACRC,MAAM,EAAEA,CAAA,KAAM8E,cAAc,CAAC,MAAM,CAAE;MACrC7E,YAAY,EAAEA,CAAA,KAAMuE,aAAa,CAAC,IAAI;IAAE;MAAA/K,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC,gBAEFpD,OAAA,CAAAE,SAAA;MAAAQ,QAAA,gBAEEV,OAAA;QAAS8D,SAAS,EAAC,MAAM;QAAApD,QAAA,gBACvBV,OAAA;UAAK8D,SAAS,EAAC,cAAc;UAAApD,QAAA,gBAC3BV,OAAA;YAAAU,QAAA,EAAI;UAA2B;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpCpD,OAAA;YAAAU,QAAA,EAAG;UAAyD;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACNpD,OAAA;UAAK8D,SAAS,EAAC,YAAY;UAAApD,QAAA,eACzBV,OAAA;YAAKiI,GAAG,EAAC,oFAAoF;YAACa,GAAG,EAAC;UAAmB;YAAA7F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVpD,OAAA;QAAS8D,SAAS,EAAC,gBAAgB;QAAApD,QAAA,eACjCV,OAAA;UAAK8D,SAAS,EAAC,kBAAkB;UAAApD,QAAA,gBAC/BV,OAAA;YAAK8D,SAAS,EAAC,YAAY;YAAApD,QAAA,gBACzBV,OAAA;cACEiH,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,kBAAkB;cAC9BlE,KAAK,EAAEiL,WAAY;cACnB9G,QAAQ,EAAGrC,CAAC,IAAKoJ,cAAc,CAACpJ,CAAC,CAACsC,MAAM,CAACpE,KAAK,CAAE;cAChDiM,UAAU,EAAGnK,CAAC,IAAKA,CAAC,CAACoK,GAAG,KAAK,OAAO,IAAIR,YAAY,CAAC;YAAE;cAAAzL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACFpD,OAAA;cAAQ+D,OAAO,EAAE2K,YAAa;cAAC5K,SAAS,EAAC,iBAAiB;cAAApD,QAAA,EAAC;YAAM;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNpD,OAAA;YAAK8D,SAAS,EAAC,YAAY;YAAApD,QAAA,eACzBV,OAAA;cACEgD,KAAK,EAAEmL,gBAAiB;cACxBhH,QAAQ,EAAGrC,CAAC,IAAKsJ,mBAAmB,CAACtJ,CAAC,CAACsC,MAAM,CAACpE,KAAK,CAAE;cAAAtC,QAAA,gBAErDV,OAAA;gBAAQgD,KAAK,EAAC,EAAE;gBAAAtC,QAAA,EAAC;cAAc;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACvCuK,UAAU,CAAClI,GAAG,CAACyD,QAAQ,iBACtBlJ,OAAA;gBAAuBgD,KAAK,EAAEkG,QAAS;gBAAAxI,QAAA,EAAEwI;cAAQ,GAApCA,QAAQ;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAqC,CAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVpD,OAAA;QAAS8D,SAAS,EAAC,YAAY;QAAApD,QAAA,EAC5B+M,MAAM,CAACxC,MAAM,KAAK,CAAC,gBAClBjL,OAAA;UAAK8D,SAAS,EAAC,WAAW;UAAApD,QAAA,gBACxBV,OAAA;YAAAU,QAAA,EAAI;UAAe;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBpD,OAAA;YAAAU,QAAA,EAAG;UAAoC;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,gBAENpD,OAAA;UAAK8D,SAAS,EAAC,kBAAkB;UAAApD,QAAA,EAC9B+M,MAAM,CAAChI,GAAG,CAAC2C,KAAK,iBACfpI,OAAA,CAACmI,SAAS;YAERC,KAAK,EAAEA,KAAM;YACbC,MAAM,EAAEyF,eAAgB;YACxBxF,SAAS,EAAEwG,kBAAmB;YAC9BvG,QAAQ,EAAEwG,iBAAkB;YAC5BvG,OAAO,EAAE,CAAA5H,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyK,QAAQ,KAAI;UAAM,GAL5BjD,KAAK,CAACO,EAAE;YAAA1F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMd,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA,eACV,CACH,EAGAyK,YAAY,iBACX7N,OAAA,CAACuH,WAAW;MACVC,OAAO,EAAEqG,YAAa;MACtBpG,OAAO,EAAEA,CAAA,KAAMqG,eAAe,CAAC,IAAI;IAAE;MAAA7K,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF,EAEA2K,UAAU,iBACT/N,OAAA,CAACsM,UAAU;MACT7E,OAAO,EAAEA,CAAA,KAAMuG,aAAa,CAAC,KAAK,CAAE;MACpCzB,SAAS,EAAEqC;IAAoB;MAAA3L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAoK,GAAA,CA1MMD,OAAO;EAAA,QACcjK,OAAO;AAAA;AAAA6L,IAAA,GAD5B5B,OAAO;AA2Mb,MAAM6B,eAAe,GAAGA,CAAC;EAAEhH,KAAK;EAAEC,MAAM;EAAEgH;AAAW,CAAC,KAAK;EACzD,MAAMzG,YAAY,GAAGR,KAAK,CAACS,SAAS,GAChC,GAAG1I,WAAW,eAAeiI,KAAK,CAACO,EAAE,YAAY,GACjD,mFAAmF;EAEvF,oBACE3I,OAAA;IAAK8D,SAAS,EAAC,YAAY;IAAApD,QAAA,gBACzBV,OAAA;MAAK8D,SAAS,EAAC,iBAAiB;MAACC,OAAO,EAAEA,CAAA,KAAMsE,MAAM,CAACD,KAAK,CAACO,EAAE,CAAE;MAAAjI,QAAA,gBAC/DV,OAAA;QACEiI,GAAG,EAAEW,YAAa;QAClBE,GAAG,EAAEV,KAAK,CAACW;MAAM;QAAA9F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACFpD,OAAA;QAAK8D,SAAS,EAAC,cAAc;QAAApD,QAAA,eAC3BV,OAAA;UAAK8D,SAAS,EAAC,aAAa;UAAApD,QAAA,EAAC;QAAC;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACNpD,OAAA;QAAK8D,SAAS,EAAC,gBAAgB;QAAApD,QAAA,EAAE0H,KAAK,CAACkH,QAAQ,IAAI;MAAO;QAAArM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,eACNpD,OAAA;MAAK8D,SAAS,EAAC,YAAY;MAAApD,QAAA,gBACzBV,OAAA;QAAI8D,SAAS,EAAC,aAAa;QAAApD,QAAA,EAAE0H,KAAK,CAACW;MAAK;QAAA9F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC9CpD,OAAA;QAAG8D,SAAS,EAAC,mBAAmB;QAAApD,QAAA,EAAE0H,KAAK,CAACa;MAAW;QAAAhG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxDpD,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAApD,QAAA,gBACzBV,OAAA;UAAM8D,SAAS,EAAC,gBAAgB;UAAApD,QAAA,EAAE0H,KAAK,CAACc;QAAQ;UAAAjG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxDpD,OAAA;UAAM8D,SAAS,EAAC,aAAa;UAAApD,QAAA,GAAE0H,KAAK,CAACe,KAAK,EAAC,QAAM;QAAA;UAAAlG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACNpD,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAApD,QAAA,EACxB0H,KAAK,CAACgB,IAAI,CAAC3D,GAAG,CAAC4D,GAAG,iBACjBrJ,OAAA;UAAgB8D,SAAS,EAAC,KAAK;UAAApD,QAAA,EAAE2I;QAAG,GAAzBA,GAAG;UAAApG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA6B,CAC5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAmM,IAAA,GAlCMH,eAAe;AAmCrB,MAAMI,iBAAiB,GAAGA,CAAC;EAAEhI,OAAO;EAAEC,OAAO;EAAE4H;AAAW,CAAC,KAAK;EAAAI,GAAA;EAC9D,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjQ,QAAQ,CAAC,KAAK,CAAC;EAE/DC,SAAS,CAAC,MAAM;IACd;IACA,MAAMiQ,KAAK,GAAG/N,UAAU,CAAC,MAAM;MAC7B8N,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMxN,YAAY,CAACyN,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMjI,QAAQ,GAAG,GAAGxH,WAAW,eAAeqH,OAAO,SAAS;EAE9D,oBACExH,OAAA;IAAK8D,SAAS,EAAC,eAAe;IAACC,OAAO,EAAE0D,OAAQ;IAAA/G,QAAA,eAC9CV,OAAA;MAAK8D,SAAS,EAAC,2BAA2B;MAACC,OAAO,EAAGe,CAAC,IAAKA,CAAC,CAAC+K,eAAe,CAAC,CAAE;MAAAnP,QAAA,gBAC7EV,OAAA;QAAQ8D,SAAS,EAAC,aAAa;QAACC,OAAO,EAAE0D,OAAQ;QAAA/G,QAAA,EAAC;MAAC;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC5DpD,OAAA;QAAK8D,SAAS,EAAC,iBAAiB;QAAApD,QAAA,eAC9BV,OAAA;UACE+H,QAAQ;UACRC,QAAQ;UACRlE,SAAS,EAAC,cAAc;UACxBmE,GAAG,EAAEN,QAAS;UAAAjH,QAAA,EACf;QAED;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAELsM,gBAAgB,iBACf1P,OAAA;QAAK8D,SAAS,EAAC,gBAAgB;QAAApD,QAAA,eAC7BV,OAAA;UAAK8D,SAAS,EAAC,gBAAgB;UAAApD,QAAA,gBAC7BV,OAAA;YAAAU,QAAA,EAAI;UAAqB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BpD,OAAA;YAAAU,QAAA,EAAG;UAA6D;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpEpD,OAAA;YAAK8D,SAAS,EAAC,gBAAgB;YAAApD,QAAA,gBAC7BV,OAAA;cAAQ+D,OAAO,EAAEsL,UAAW;cAACvL,SAAS,EAAC,iBAAiB;cAAApD,QAAA,EAAC;YAEzD;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpD,OAAA;cACE+D,OAAO,EAAEA,CAAA,KAAM4L,mBAAmB,CAAC,KAAK,CAAE;cAC1C7L,SAAS,EAAC,mBAAmB;cAAApD,QAAA,EAC9B;YAED;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAqM,GAAA,CArDMD,iBAAiB;AAAAM,IAAA,GAAjBN,iBAAiB;AAsDvB,MAAMO,SAAS,GAAGA,CAAC;EAAEV;AAAW,CAAC,KAAK;EAAAW,IAAA;EACpC,MAAM,CAACvC,MAAM,EAAEC,SAAS,CAAC,GAAGhO,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiO,UAAU,EAAEC,aAAa,CAAC,GAAGlO,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuO,WAAW,EAAEC,cAAc,CAAC,GAAGxO,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1O,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACmO,YAAY,EAAEC,eAAe,CAAC,GAAGpO,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuQ,YAAY,EAAEC,eAAe,CAAC,GAAGxQ,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM;IAAE0B;EAAiB,CAAC,GAAGkC,OAAO,CAAC,CAAC;EAEtC3D,SAAS,CAAC,MAAM;IACd,MAAMwQ,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF;QACA,MAAMC,OAAO,CAACC,UAAU,CAAC,CACvB9B,WAAW,CAAC,CAAC,EACbC,eAAe,CAAC,CAAC,CAClB,CAAC;MACJ,CAAC,CAAC,OAAOnM,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC7C,CAAC,SAAS;QACR;QACAlB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDgP,QAAQ,CAAC,CAAC;;IAEV;IACA,MAAMvO,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjCV,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMgB,YAAY,CAACP,SAAS,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM2M,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF;MACA,MAAM7M,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;MAE5D,MAAMC,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAAC,GAAGzB,GAAG,SAAS,EAAE;QAChD0B,MAAM,EAAEP,UAAU,CAACO,MAAM;QACzBC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFC,YAAY,CAACP,SAAS,CAAC;MACvB8L,SAAS,CAAC3L,QAAQ,CAACK,IAAI,IAAI,EAAE,CAAC;MAC9B8N,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAO7N,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CqL,SAAS,CAAC,EAAE,CAAC;;MAEb;MACA,IAAIrL,KAAK,CAACE,IAAI,KAAK,cAAc,IAAIF,KAAK,CAACG,IAAI,KAAK,YAAY,IAAI,CAACH,KAAK,CAACN,QAAQ,EAAE;QACnFmO,eAAe,CAAC,IAAI,CAAC;QACrB5N,OAAO,CAACG,IAAI,CAAC,sDAAsD,CAAC;MACtE;IACF;IACA;EACF,CAAC;EAED,MAAM+L,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF;MACA,MAAM9M,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;MAE5D,MAAMC,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAAC,GAAGzB,GAAG,aAAa,EAAE;QACpD0B,MAAM,EAAEP,UAAU,CAACO,MAAM;QACzBC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFC,YAAY,CAACP,SAAS,CAAC;MACvBgM,aAAa,CAAC7L,QAAQ,CAACK,IAAI,IAAI,EAAE,CAAC;IACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDuL,aAAa,CAAC,EAAE,CAAC;;MAEjB;MACA,IAAIvL,KAAK,CAACE,IAAI,KAAK,cAAc,IAAIF,KAAK,CAACG,IAAI,KAAK,YAAY,IAAI,CAACH,KAAK,CAACN,QAAQ,EAAE;QACnFO,OAAO,CAACG,IAAI,CAAC,yDAAyD,CAAC;MACzE;IACF;EACF,CAAC;EAED,MAAMiM,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,MAAMhN,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;MAE5D,MAAMC,QAAQ,GAAG,MAAMjC,KAAK,CAACmF,IAAI,CAAC,GAAG1E,GAAG,SAAS,EAAE;QACjDoO,KAAK,EAAEV,WAAW;QAClB/E,QAAQ,EAAEiF;MACZ,CAAC,EAAE;QACDlM,MAAM,EAAEP,UAAU,CAACO,MAAM;QACzBC,OAAO,EAAE;MACX,CAAC,CAAC;MAEFC,YAAY,CAACP,SAAS,CAAC;MACvB8L,SAAS,CAAC3L,QAAQ,CAACK,IAAI,IAAI,EAAE,CAAC;MAC9B8N,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAO7N,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;;MAEhD;MACA,IAAIA,KAAK,CAACE,IAAI,KAAK,cAAc,IAAIF,KAAK,CAACG,IAAI,KAAK,YAAY,IAAI,CAACH,KAAK,CAACN,QAAQ,EAAE;QACnFmO,eAAe,CAAC,IAAI,CAAC;QACrB5N,OAAO,CAACG,IAAI,CAAC,8CAA8C,CAAC;MAC9D;IACF;EACF,CAAC;EAED,IAAIvB,OAAO,EAAE;IACX,oBAAOlB,OAAA;MAAK8D,SAAS,EAAC,SAAS;MAAApD,QAAA,EAAC;IAAU;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAClD;EAEA,oBACEpD,OAAA;IAAK8D,SAAS,EAAC,KAAK;IAAApD,QAAA,GAEjB,CAACuP,YAAY,IAAI,CAAC7O,gBAAgB,kBACjCpB,OAAA;MAAK8D,SAAS,EAAC,0BAA0B;MAAApD,QAAA,eACvCV,OAAA;QAAK8D,SAAS,EAAC,2BAA2B;QAAApD,QAAA,gBACxCV,OAAA;UAAM8D,SAAS,EAAC,wBAAwB;UAAApD,QAAA,EAAC;QAAE;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClDpD,OAAA;UAAM8D,SAAS,EAAC,wBAAwB;UAAApD,QAAA,EAAC;QAEzC;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDpD,OAAA;MAAQ8D,SAAS,EAAC,QAAQ;MAAApD,QAAA,eACxBV,OAAA;QAAK8D,SAAS,EAAC,gBAAgB;QAAApD,QAAA,gBAC7BV,OAAA;UAAI8D,SAAS,EAAC,MAAM;UAAApD,QAAA,EAAC;QAAS;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnCpD,OAAA;UAAK8D,SAAS,EAAC,gBAAgB;UAAApD,QAAA,eAC7BV,OAAA;YAAQ+D,OAAO,EAAEsL,UAAW;YAACvL,SAAS,EAAC,iBAAiB;YAAApD,QAAA,EAAC;UAEzD;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTpD,OAAA;MAAS8D,SAAS,EAAC,MAAM;MAAApD,QAAA,gBACvBV,OAAA;QAAK8D,SAAS,EAAC,cAAc;QAAApD,QAAA,gBAC3BV,OAAA;UAAAU,QAAA,EAAI;QAA2B;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpCpD,OAAA;UAAAU,QAAA,EAAG;QAAyD;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACNpD,OAAA;QAAK8D,SAAS,EAAC,YAAY;QAAApD,QAAA,eACzBV,OAAA;UAAKiI,GAAG,EAAC,oFAAoF;UAACa,GAAG,EAAC;QAAmB;UAAA7F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGTpD,OAAA;MAAS8D,SAAS,EAAC,gBAAgB;MAAApD,QAAA,eACjCV,OAAA;QAAK8D,SAAS,EAAC,kBAAkB;QAAApD,QAAA,gBAC/BV,OAAA;UAAK8D,SAAS,EAAC,YAAY;UAAApD,QAAA,gBACzBV,OAAA;YACEiH,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,kBAAkB;YAC9BlE,KAAK,EAAEiL,WAAY;YACnB9G,QAAQ,EAAGrC,CAAC,IAAKoJ,cAAc,CAACpJ,CAAC,CAACsC,MAAM,CAACpE,KAAK,CAAE;YAChDiM,UAAU,EAAGnK,CAAC,IAAKA,CAAC,CAACoK,GAAG,KAAK,OAAO,IAAIR,YAAY,CAAC;UAAE;YAAAzL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACFpD,OAAA;YAAQ+D,OAAO,EAAE2K,YAAa;YAAC5K,SAAS,EAAC,iBAAiB;YAAApD,QAAA,EAAC;UAAM;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACNpD,OAAA;UAAK8D,SAAS,EAAC,YAAY;UAAApD,QAAA,eACzBV,OAAA;YACEgD,KAAK,EAAEmL,gBAAiB;YACxBhH,QAAQ,EAAGrC,CAAC,IAAKsJ,mBAAmB,CAACtJ,CAAC,CAACsC,MAAM,CAACpE,KAAK,CAAE;YAAAtC,QAAA,gBAErDV,OAAA;cAAQgD,KAAK,EAAC,EAAE;cAAAtC,QAAA,EAAC;YAAc;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACvCmC,KAAK,CAACC,OAAO,CAACmI,UAAU,CAAC,IAAIA,UAAU,CAAClI,GAAG,CAACyD,QAAQ,iBACnDlJ,OAAA;cAA0BgD,KAAK,EAAEkG,QAAQ,CAAC1G,IAAK;cAAA9B,QAAA,EAAEwI,QAAQ,CAAC1G;YAAI,GAAjD0G,QAAQ,CAACP,EAAE;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA+C,CACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVpD,OAAA;MAAS8D,SAAS,EAAC,YAAY;MAAApD,QAAA,EAC5B,CAAC6E,KAAK,CAACC,OAAO,CAACiI,MAAM,CAAC,IAAIA,MAAM,CAACxC,MAAM,KAAK,CAAC,gBAC5CjL,OAAA;QAAK8D,SAAS,EAAC,WAAW;QAAApD,QAAA,gBACxBV,OAAA;UAAAU,QAAA,EAAI;QAAe;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBpD,OAAA;UAAAU,QAAA,EAAG;QAAoC;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,gBAENpD,OAAA;QAAK8D,SAAS,EAAC,kBAAkB;QAAApD,QAAA,EAC9B+M,MAAM,CAAChI,GAAG,CAAC2C,KAAK,iBACfpI,OAAA,CAACoP,eAAe;UAEdhH,KAAK,EAAEA,KAAM;UACbC,MAAM,EAAEyF,eAAgB;UACxBuB,UAAU,EAAEA;QAAW,GAHlBjH,KAAK,CAACO,EAAE;UAAA1F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAId,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,EAGTyK,YAAY,iBACX7N,OAAA,CAACwP,iBAAiB;MAChBhI,OAAO,EAAEqG,YAAa;MACtBpG,OAAO,EAAEA,CAAA,KAAMqG,eAAe,CAAC,IAAI,CAAE;MACrCuB,UAAU,EAAEA;IAAW;MAAApM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAA4M,IAAA,CAxNMD,SAAS;EAAA,QAQgBzM,OAAO;AAAA;AAAAgN,IAAA,GARhCP,SAAS;AAyNf,MAAMQ,YAAY,GAAGA,CAAA,KAAM;EAAAC,IAAA;EACzB,MAAM;IAAE5P;EAAK,CAAC,GAAG0C,OAAO,CAAC,CAAC;EAE1B3D,SAAS,CAAC,MAAM;IACd;IACA,IAAIiB,IAAI,EAAE;MACRuF,MAAM,CAACK,QAAQ,CAACiK,IAAI,GAAG,GAAG;IAC5B,CAAC,MAAM;MACL;MACA5O,UAAU,CAAC,MAAM;QACfsE,MAAM,CAACK,QAAQ,CAACiK,IAAI,GAAG,GAAG;MAC5B,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,EAAE,CAAC7P,IAAI,CAAC,CAAC;EAEV,oBACEZ,OAAA;IAAK8D,SAAS,EAAC,SAAS;IAAApD,QAAA,eACtBV,OAAA;MAAAU,QAAA,EAAG;IAAc;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC;AAEV,CAAC;;AAED;AAAAoN,IAAA,CAtBMD,YAAY;EAAA,QACCjN,OAAO;AAAA;AAAAoN,IAAA,GADpBH,YAAY;AAuBlB,MAAMI,GAAG,GAAGA,CAAA,KAAM;EAChB,oBACE3Q,OAAA,CAACS,YAAY;IAAAC,QAAA,eACXV,OAAA,CAAC4Q,SAAS;MAAA3N,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEnB,CAAC;AAACyN,IAAA,GANIF,GAAG;AAQT,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAE,IAAA;EACtB,MAAM;IAAElQ,IAAI;IAAEM;EAAQ,CAAC,GAAGoC,OAAO,CAAC,CAAC;EACnC,MAAM,CAACyN,aAAa,EAAEC,gBAAgB,CAAC,GAAGtR,QAAQ,CAAC,KAAK,CAAC;EAEzD,IAAIwB,OAAO,EAAE;IACX,oBAAOlB,OAAA;MAAK8D,SAAS,EAAC,SAAS;MAAApD,QAAA,EAAC;IAAU;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAClD;;EAEA;EACA,IAAI+C,MAAM,CAACK,QAAQ,CAACyK,QAAQ,KAAK,gBAAgB,EAAE;IACjD,oBAAOjR,OAAA,CAACuQ,YAAY;MAAAtN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzB;;EAEA;EACA,IAAI,CAACxC,IAAI,EAAE;IACT,oBACEZ,OAAA,CAAAE,SAAA;MAAAQ,QAAA,gBACEV,OAAA,CAAC+P,SAAS;QAACV,UAAU,EAAEA,CAAA,KAAM2B,gBAAgB,CAAC,IAAI;MAAE;QAAA/N,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACtD2N,aAAa,iBACZ/Q,OAAA;QAAK8D,SAAS,EAAC,eAAe;QAAApD,QAAA,eAC5BV,OAAA;UAAK8D,SAAS,EAAC,0BAA0B;UAAApD,QAAA,gBACvCV,OAAA;YACE8D,SAAS,EAAC,aAAa;YACvBC,OAAO,EAAEA,CAAA,KAAMiN,gBAAgB,CAAC,KAAK,CAAE;YAAAtQ,QAAA,EACxC;UAED;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpD,OAAA,CAACiE,SAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA,eACD,CAAC;EAEP;EAEA,IAAI,CAACxC,IAAI,CAAC6D,YAAY,EAAE;IACtB,oBACEzE,OAAA;MAAK8D,SAAS,EAAC,iBAAiB;MAAApD,QAAA,gBAC9BV,OAAA;QAAAU,QAAA,EAAI;MAAyB;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClCpD,OAAA;QAAAU,QAAA,EAAG;MAAgD;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC;EAEV;EAEA,oBAAOpD,OAAA,CAACuN,OAAO;IAAAtK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACpB,CAAC;AAAC0N,IAAA,CA7CIF,SAAS;EAAA,QACatN,OAAO;AAAA;AAAA4N,IAAA,GAD7BN,SAAS;AA+Cf,eAAeD,GAAG;AAAC,IAAAtN,EAAA,EAAAW,GAAA,EAAAsD,GAAA,EAAAY,GAAA,EAAAoB,GAAA,EAAAqC,GAAA,EAAAC,GAAA,EAAAS,GAAA,EAAAiB,GAAA,EAAA6B,IAAA,EAAAI,IAAA,EAAAO,IAAA,EAAAQ,IAAA,EAAAI,IAAA,EAAAG,IAAA,EAAAK,IAAA;AAAAC,YAAA,CAAA9N,EAAA;AAAA8N,YAAA,CAAAnN,GAAA;AAAAmN,YAAA,CAAA7J,GAAA;AAAA6J,YAAA,CAAAjJ,GAAA;AAAAiJ,YAAA,CAAA7H,GAAA;AAAA6H,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAvF,GAAA;AAAAuF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAAhC,IAAA;AAAAgC,YAAA,CAAA5B,IAAA;AAAA4B,YAAA,CAAArB,IAAA;AAAAqB,YAAA,CAAAb,IAAA;AAAAa,YAAA,CAAAT,IAAA;AAAAS,YAAA,CAAAN,IAAA;AAAAM,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}