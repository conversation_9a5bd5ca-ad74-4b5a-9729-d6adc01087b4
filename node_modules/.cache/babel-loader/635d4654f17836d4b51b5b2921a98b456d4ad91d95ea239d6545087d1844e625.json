{"ast": null, "code": "/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\n\"production\" !== process.env.NODE_ENV && function () {\n  function getComponentNameFromType(type) {\n    if (null == type) return null;\n    if (\"function\" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE$2 ? null : type.displayName || type.name || null;\n    if (\"string\" === typeof type) return type;\n    switch (type) {\n      case REACT_FRAGMENT_TYPE:\n        return \"Fragment\";\n      case REACT_PORTAL_TYPE:\n        return \"Portal\";\n      case REACT_PROFILER_TYPE:\n        return \"Profiler\";\n      case REACT_STRICT_MODE_TYPE:\n        return \"StrictMode\";\n      case REACT_SUSPENSE_TYPE:\n        return \"Suspense\";\n      case REACT_SUSPENSE_LIST_TYPE:\n        return \"SuspenseList\";\n    }\n    if (\"object\" === typeof type) switch (\"number\" === typeof type.tag && console.error(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"), type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        return (type.displayName || \"Context\") + \".Provider\";\n      case REACT_CONSUMER_TYPE:\n        return (type._context.displayName || \"Context\") + \".Consumer\";\n      case REACT_FORWARD_REF_TYPE:\n        var innerType = type.render;\n        type = type.displayName;\n        type || (type = innerType.displayName || innerType.name || \"\", type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\");\n        return type;\n      case REACT_MEMO_TYPE:\n        return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || \"Memo\";\n      case REACT_LAZY_TYPE:\n        innerType = type._payload;\n        type = type._init;\n        try {\n          return getComponentNameFromType(type(innerType));\n        } catch (x) {}\n    }\n    return null;\n  }\n  function testStringCoercion(value) {\n    return \"\" + value;\n  }\n  function checkKeyStringCoercion(value) {\n    try {\n      testStringCoercion(value);\n      var JSCompiler_inline_result = !1;\n    } catch (e) {\n      JSCompiler_inline_result = !0;\n    }\n    if (JSCompiler_inline_result) {\n      JSCompiler_inline_result = console;\n      var JSCompiler_temp_const = JSCompiler_inline_result.error;\n      var JSCompiler_inline_result$jscomp$0 = \"function\" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || \"Object\";\n      JSCompiler_temp_const.call(JSCompiler_inline_result, \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\", JSCompiler_inline_result$jscomp$0);\n      return testStringCoercion(value);\n    }\n  }\n  function disabledLog() {}\n  function disableLogs() {\n    if (0 === disabledDepth) {\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd;\n      var props = {\n        configurable: !0,\n        enumerable: !0,\n        value: disabledLog,\n        writable: !0\n      };\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n    }\n    disabledDepth++;\n  }\n  function reenableLogs() {\n    disabledDepth--;\n    if (0 === disabledDepth) {\n      var props = {\n        configurable: !0,\n        enumerable: !0,\n        writable: !0\n      };\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n    }\n    0 > disabledDepth && console.error(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\");\n  }\n  function describeBuiltInComponentFrame(name) {\n    if (void 0 === prefix) try {\n      throw Error();\n    } catch (x) {\n      var match = x.stack.trim().match(/\\n( *(at )?)/);\n      prefix = match && match[1] || \"\";\n      suffix = -1 < x.stack.indexOf(\"\\n    at\") ? \" (<anonymous>)\" : -1 < x.stack.indexOf(\"@\") ? \"@unknown:0:0\" : \"\";\n    }\n    return \"\\n\" + prefix + name + suffix;\n  }\n  function describeNativeComponentFrame(fn, construct) {\n    if (!fn || reentry) return \"\";\n    var frame = componentFrameCache.get(fn);\n    if (void 0 !== frame) return frame;\n    reentry = !0;\n    frame = Error.prepareStackTrace;\n    Error.prepareStackTrace = void 0;\n    var previousDispatcher = null;\n    previousDispatcher = ReactSharedInternals.H;\n    ReactSharedInternals.H = null;\n    disableLogs();\n    try {\n      var RunInRootFrame = {\n        DetermineComponentFrameRoot: function () {\n          try {\n            if (construct) {\n              var Fake = function () {\n                throw Error();\n              };\n              Object.defineProperty(Fake.prototype, \"props\", {\n                set: function () {\n                  throw Error();\n                }\n              });\n              if (\"object\" === typeof Reflect && Reflect.construct) {\n                try {\n                  Reflect.construct(Fake, []);\n                } catch (x) {\n                  var control = x;\n                }\n                Reflect.construct(fn, [], Fake);\n              } else {\n                try {\n                  Fake.call();\n                } catch (x$0) {\n                  control = x$0;\n                }\n                fn.call(Fake.prototype);\n              }\n            } else {\n              try {\n                throw Error();\n              } catch (x$1) {\n                control = x$1;\n              }\n              (Fake = fn()) && \"function\" === typeof Fake.catch && Fake.catch(function () {});\n            }\n          } catch (sample) {\n            if (sample && control && \"string\" === typeof sample.stack) return [sample.stack, control.stack];\n          }\n          return [null, null];\n        }\n      };\n      RunInRootFrame.DetermineComponentFrameRoot.displayName = \"DetermineComponentFrameRoot\";\n      var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, \"name\");\n      namePropDescriptor && namePropDescriptor.configurable && Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, \"name\", {\n        value: \"DetermineComponentFrameRoot\"\n      });\n      var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n      if (sampleStack && controlStack) {\n        var sampleLines = sampleStack.split(\"\\n\"),\n          controlLines = controlStack.split(\"\\n\");\n        for (_RunInRootFrame$Deter = namePropDescriptor = 0; namePropDescriptor < sampleLines.length && !sampleLines[namePropDescriptor].includes(\"DetermineComponentFrameRoot\");) namePropDescriptor++;\n        for (; _RunInRootFrame$Deter < controlLines.length && !controlLines[_RunInRootFrame$Deter].includes(\"DetermineComponentFrameRoot\");) _RunInRootFrame$Deter++;\n        if (namePropDescriptor === sampleLines.length || _RunInRootFrame$Deter === controlLines.length) for (namePropDescriptor = sampleLines.length - 1, _RunInRootFrame$Deter = controlLines.length - 1; 1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter && sampleLines[namePropDescriptor] !== controlLines[_RunInRootFrame$Deter];) _RunInRootFrame$Deter--;\n        for (; 1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter; namePropDescriptor--, _RunInRootFrame$Deter--) if (sampleLines[namePropDescriptor] !== controlLines[_RunInRootFrame$Deter]) {\n          if (1 !== namePropDescriptor || 1 !== _RunInRootFrame$Deter) {\n            do if (namePropDescriptor--, _RunInRootFrame$Deter--, 0 > _RunInRootFrame$Deter || sampleLines[namePropDescriptor] !== controlLines[_RunInRootFrame$Deter]) {\n              var _frame = \"\\n\" + sampleLines[namePropDescriptor].replace(\" at new \", \" at \");\n              fn.displayName && _frame.includes(\"<anonymous>\") && (_frame = _frame.replace(\"<anonymous>\", fn.displayName));\n              \"function\" === typeof fn && componentFrameCache.set(fn, _frame);\n              return _frame;\n            } while (1 <= namePropDescriptor && 0 <= _RunInRootFrame$Deter);\n          }\n          break;\n        }\n      }\n    } finally {\n      reentry = !1, ReactSharedInternals.H = previousDispatcher, reenableLogs(), Error.prepareStackTrace = frame;\n    }\n    sampleLines = (sampleLines = fn ? fn.displayName || fn.name : \"\") ? describeBuiltInComponentFrame(sampleLines) : \"\";\n    \"function\" === typeof fn && componentFrameCache.set(fn, sampleLines);\n    return sampleLines;\n  }\n  function describeUnknownElementTypeFrameInDEV(type) {\n    if (null == type) return \"\";\n    if (\"function\" === typeof type) {\n      var prototype = type.prototype;\n      return describeNativeComponentFrame(type, !(!prototype || !prototype.isReactComponent));\n    }\n    if (\"string\" === typeof type) return describeBuiltInComponentFrame(type);\n    switch (type) {\n      case REACT_SUSPENSE_TYPE:\n        return describeBuiltInComponentFrame(\"Suspense\");\n      case REACT_SUSPENSE_LIST_TYPE:\n        return describeBuiltInComponentFrame(\"SuspenseList\");\n    }\n    if (\"object\" === typeof type) switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return type = describeNativeComponentFrame(type.render, !1), type;\n      case REACT_MEMO_TYPE:\n        return describeUnknownElementTypeFrameInDEV(type.type);\n      case REACT_LAZY_TYPE:\n        prototype = type._payload;\n        type = type._init;\n        try {\n          return describeUnknownElementTypeFrameInDEV(type(prototype));\n        } catch (x) {}\n    }\n    return \"\";\n  }\n  function getOwner() {\n    var dispatcher = ReactSharedInternals.A;\n    return null === dispatcher ? null : dispatcher.getOwner();\n  }\n  function hasValidKey(config) {\n    if (hasOwnProperty.call(config, \"key\")) {\n      var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n      if (getter && getter.isReactWarning) return !1;\n    }\n    return void 0 !== config.key;\n  }\n  function defineKeyPropWarningGetter(props, displayName) {\n    function warnAboutAccessingKey() {\n      specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\", displayName));\n    }\n    warnAboutAccessingKey.isReactWarning = !0;\n    Object.defineProperty(props, \"key\", {\n      get: warnAboutAccessingKey,\n      configurable: !0\n    });\n  }\n  function elementRefGetterWithDeprecationWarning() {\n    var componentName = getComponentNameFromType(this.type);\n    didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error(\"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"));\n    componentName = this.props.ref;\n    return void 0 !== componentName ? componentName : null;\n  }\n  function ReactElement(type, key, self, source, owner, props) {\n    self = props.ref;\n    type = {\n      $$typeof: REACT_ELEMENT_TYPE,\n      type: type,\n      key: key,\n      props: props,\n      _owner: owner\n    };\n    null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, \"ref\", {\n      enumerable: !1,\n      get: elementRefGetterWithDeprecationWarning\n    }) : Object.defineProperty(type, \"ref\", {\n      enumerable: !1,\n      value: null\n    });\n    type._store = {};\n    Object.defineProperty(type._store, \"validated\", {\n      configurable: !1,\n      enumerable: !1,\n      writable: !0,\n      value: 0\n    });\n    Object.defineProperty(type, \"_debugInfo\", {\n      configurable: !1,\n      enumerable: !1,\n      writable: !0,\n      value: null\n    });\n    Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n    return type;\n  }\n  function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self) {\n    if (\"string\" === typeof type || \"function\" === typeof type || type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_OFFSCREEN_TYPE || \"object\" === typeof type && null !== type && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_CONSUMER_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_CLIENT_REFERENCE$1 || void 0 !== type.getModuleId)) {\n      var children = config.children;\n      if (void 0 !== children) if (isStaticChildren) {\n        if (isArrayImpl(children)) {\n          for (isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++) validateChildKeys(children[isStaticChildren], type);\n          Object.freeze && Object.freeze(children);\n        } else console.error(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");\n      } else validateChildKeys(children, type);\n    } else {\n      children = \"\";\n      if (void 0 === type || \"object\" === typeof type && null !== type && 0 === Object.keys(type).length) children += \" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\";\n      null === type ? isStaticChildren = \"null\" : isArrayImpl(type) ? isStaticChildren = \"array\" : void 0 !== type && type.$$typeof === REACT_ELEMENT_TYPE ? (isStaticChildren = \"<\" + (getComponentNameFromType(type.type) || \"Unknown\") + \" />\", children = \" Did you accidentally export a JSX literal instead of a component?\") : isStaticChildren = typeof type;\n      console.error(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\", isStaticChildren, children);\n    }\n    if (hasOwnProperty.call(config, \"key\")) {\n      children = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return \"key\" !== k;\n      });\n      isStaticChildren = 0 < keys.length ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\" : \"{key: someKey}\";\n      didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\", console.error('A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);\n    }\n    children = null;\n    void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = \"\" + maybeKey);\n    hasValidKey(config) && (checkKeyStringCoercion(config.key), children = \"\" + config.key);\n    if (\"key\" in config) {\n      maybeKey = {};\n      for (var propName in config) \"key\" !== propName && (maybeKey[propName] = config[propName]);\n    } else maybeKey = config;\n    children && defineKeyPropWarningGetter(maybeKey, \"function\" === typeof type ? type.displayName || type.name || \"Unknown\" : type);\n    return ReactElement(type, children, self, source, getOwner(), maybeKey);\n  }\n  function validateChildKeys(node, parentType) {\n    if (\"object\" === typeof node && node && node.$$typeof !== REACT_CLIENT_REFERENCE) if (isArrayImpl(node)) for (var i = 0; i < node.length; i++) {\n      var child = node[i];\n      isValidElement(child) && validateExplicitKey(child, parentType);\n    } else if (isValidElement(node)) node._store && (node._store.validated = 1);else if (null === node || \"object\" !== typeof node ? i = null : (i = MAYBE_ITERATOR_SYMBOL && node[MAYBE_ITERATOR_SYMBOL] || node[\"@@iterator\"], i = \"function\" === typeof i ? i : null), \"function\" === typeof i && i !== node.entries && (i = i.call(node), i !== node)) for (; !(node = i.next()).done;) isValidElement(node.value) && validateExplicitKey(node.value, parentType);\n  }\n  function isValidElement(object) {\n    return \"object\" === typeof object && null !== object && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n  function validateExplicitKey(element, parentType) {\n    if (element._store && !element._store.validated && null == element.key && (element._store.validated = 1, parentType = getCurrentComponentErrorInfo(parentType), !ownerHasKeyUseWarning[parentType])) {\n      ownerHasKeyUseWarning[parentType] = !0;\n      var childOwner = \"\";\n      element && null != element._owner && element._owner !== getOwner() && (childOwner = null, \"number\" === typeof element._owner.tag ? childOwner = getComponentNameFromType(element._owner.type) : \"string\" === typeof element._owner.name && (childOwner = element._owner.name), childOwner = \" It was passed a child from \" + childOwner + \".\");\n      var prevGetCurrentStack = ReactSharedInternals.getCurrentStack;\n      ReactSharedInternals.getCurrentStack = function () {\n        var stack = describeUnknownElementTypeFrameInDEV(element.type);\n        prevGetCurrentStack && (stack += prevGetCurrentStack() || \"\");\n        return stack;\n      };\n      console.error('Each child in a list should have a unique \"key\" prop.%s%s See https://react.dev/link/warning-keys for more information.', parentType, childOwner);\n      ReactSharedInternals.getCurrentStack = prevGetCurrentStack;\n    }\n  }\n  function getCurrentComponentErrorInfo(parentType) {\n    var info = \"\",\n      owner = getOwner();\n    owner && (owner = getComponentNameFromType(owner.type)) && (info = \"\\n\\nCheck the render method of `\" + owner + \"`.\");\n    info || (parentType = getComponentNameFromType(parentType)) && (info = \"\\n\\nCheck the top-level render call using <\" + parentType + \">.\");\n    return info;\n  }\n  var React = require(\"react\"),\n    REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n    REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n    REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n    REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n    REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n  Symbol.for(\"react.provider\");\n  var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n    REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n    REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n    REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n    REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n    REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n    REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n    REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\"),\n    MAYBE_ITERATOR_SYMBOL = Symbol.iterator,\n    REACT_CLIENT_REFERENCE$2 = Symbol.for(\"react.client.reference\"),\n    ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n    hasOwnProperty = Object.prototype.hasOwnProperty,\n    assign = Object.assign,\n    REACT_CLIENT_REFERENCE$1 = Symbol.for(\"react.client.reference\"),\n    isArrayImpl = Array.isArray,\n    disabledDepth = 0,\n    prevLog,\n    prevInfo,\n    prevWarn,\n    prevError,\n    prevGroup,\n    prevGroupCollapsed,\n    prevGroupEnd;\n  disabledLog.__reactDisabledLog = !0;\n  var prefix,\n    suffix,\n    reentry = !1;\n  var componentFrameCache = new (\"function\" === typeof WeakMap ? WeakMap : Map)();\n  var REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n    specialPropKeyWarningShown;\n  var didWarnAboutElementRef = {};\n  var didWarnAboutKeySpread = {},\n    ownerHasKeyUseWarning = {};\n  exports.Fragment = REACT_FRAGMENT_TYPE;\n  exports.jsxDEV = function (type, config, maybeKey, isStaticChildren, source, self) {\n    return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self);\n  };\n}();", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}