"use strict";
/* eslint-disable @typescript-eslint/no-empty-interface, @typescript-eslint/no-explicit-any */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClassScope = exports.ForScope = exports.FunctionScope = exports.SwitchScope = exports.BlockScope = exports.WithScope = exports.CatchScope = exports.FunctionExpressionNameScope = exports.ModuleScope = exports.GlobalScope = exports.Scope = void 0;
const scope_1 = require("eslint-scope/lib/scope");
const Scope = scope_1.Scope;
exports.Scope = Scope;
const GlobalScope = scope_1.GlobalScope;
exports.GlobalScope = GlobalScope;
const ModuleScope = scope_1.ModuleScope;
exports.ModuleScope = ModuleScope;
const FunctionExpressionNameScope = scope_1.FunctionExpressionNameScope;
exports.FunctionExpressionNameScope = FunctionExpressionNameScope;
const CatchScope = scope_1.CatchScope;
exports.CatchScope = CatchScope;
const WithScope = scope_1.WithScope;
exports.WithScope = WithScope;
const BlockScope = scope_1.BlockScope;
exports.BlockScope = BlockScope;
const SwitchScope = scope_1.SwitchScope;
exports.SwitchScope = SwitchScope;
const FunctionScope = scope_1.FunctionScope;
exports.FunctionScope = FunctionScope;
const ForScope = scope_1.ForScope;
exports.ForScope = ForScope;
const ClassScope = scope_1.ClassScope;
exports.ClassScope = ClassScope;
//# sourceMappingURL=Scope.js.map