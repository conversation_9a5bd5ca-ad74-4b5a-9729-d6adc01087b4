from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from motor.motor_asyncio import AsyncIOMotorClient
from google.oauth2 import id_token
from google.auth.transport import requests as google_requests
from pydantic import BaseModel, Field
import os
import jwt
from datetime import datetime, timedelta
import uuid
from typing import Optional

# Environment variables
mongo_url = os.environ.get('MONGO_URL', 'mongodb://localhost:27017')
db_name = os.environ.get('DB_NAME', 'adult_content_platform')
JWT_SECRET = os.environ.get('JWT_SECRET', 'your-secret-key-here')
GOOGLE_CLIENT_ID = os.environ.get('GOOGLE_CLIENT_ID')
GOOGLE_CLIENT_SECRET = os.environ.get('GOOGLE_CLIENT_SECRET')
GOOGLE_REDIRECT_URI = os.environ.get('GOOGLE_REDIRECT_URI')

# MongoDB connection
client = AsyncIOMotorClient(mongo_url)
db = client[db_name]

app = FastAPI()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class User(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    email: str
    name: str
    password_hash: Optional[str] = None
    age_verified: bool = False
    is_admin: bool = False
    is_approved: bool = False
    created_at: datetime = Field(default_factory=datetime.utcnow)
    picture: Optional[str] = None
    session_token: Optional[str] = None

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(hours=24)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET, algorithm="HS256")
    return encoded_jwt

@app.get("/login")
async def google_login():
    # Return the Google OAuth URL for frontend to redirect to
    google_auth_url = (
        f"https://accounts.google.com/o/oauth2/auth?"
        f"client_id={GOOGLE_CLIENT_ID}&"
        f"redirect_uri={GOOGLE_REDIRECT_URI}&"
        f"scope=openid email profile&"
        f"response_type=code&"
        f"access_type=offline"
    )
    return {"auth_url": google_auth_url}

@app.post("/token")
async def google_token_login(token_data: dict):
    try:
        # Verify the Google ID token
        id_info = id_token.verify_oauth2_token(
            token_data['token'], 
            google_requests.Request(), 
            GOOGLE_CLIENT_ID
        )
        
        # Check if user exists
        existing_user = await db.users.find_one({"email": id_info['email']})
        
        if existing_user:
            user = User(**existing_user)
        else:
            # Create new user
            user = User(
                email=id_info['email'],
                name=id_info['name'],
                age_verified=True,
                picture=id_info.get('picture')
            )
            await db.users.insert_one(user.dict())
        
        # Create access token
        access_token = create_access_token(data={"sub": user.id})
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "email": user.email,
                "name": user.name,
                "age_verified": user.age_verified,
                "is_admin": user.is_admin,
                "picture": user.picture
            }
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid token: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Authentication failed: {str(e)}")

@app.get("/callback")
async def google_callback(request: Request):
    # This endpoint handles the OAuth callback
    # In a serverless environment, this is simplified
    try:
        # Get the authorization code from query parameters
        code = request.query_params.get('code')
        if not code:
            raise HTTPException(status_code=400, detail="Authorization code not provided")
        
        # Exchange code for tokens (simplified - in production you'd use proper OAuth flow)
        # For now, return HTML that closes the popup
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Login Processing</title>
        </head>
        <body>
            <script>
                // Extract code from URL and send to parent
                const urlParams = new URLSearchParams(window.location.search);
                const code = urlParams.get('code');
                const error = urlParams.get('error');
                
                if (error) {
                    window.opener.postMessage({
                        'error': 'Google login failed: ' + error
                    }, '*');
                } else if (code) {
                    window.opener.postMessage({
                        'code': code
                    }, '*');
                } else {
                    window.opener.postMessage({
                        'error': 'No authorization code received'
                    }, '*');
                }
                
                window.close();
            </script>
            <p>Processing login... This window should close automatically.</p>
        </body>
        </html>
        """
        
        return HTMLResponse(content=html_content)
        
    except Exception as e:
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Login Error</title>
        </head>
        <body>
            <script>
                window.opener.postMessage({{
                    'error': 'Login failed: {str(e)}'
                }}, '*');
                window.close();
            </script>
            <p>Login failed: {str(e)}</p>
        </body>
        </html>
        """
        
        return HTMLResponse(content=html_content)

# Vercel handler
handler = app