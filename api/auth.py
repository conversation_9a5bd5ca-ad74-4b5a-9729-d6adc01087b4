from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTT<PERSON><PERSON>x<PERSON>, Request, Depends
from fastapi.security import HTTPAuthorizationCredentials
from motor.motor_asyncio import AsyncIOMotorClient
from google.oauth2 import id_token
from google.auth.transport import requests as google_requests
import os
import httpx
from datetime import datetime
from .index import (
    User, UserRegister, UserLogin, hash_password, verify_password, 
    create_access_token, verify_token, get_current_user, db, oauth,
    GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_REDIRECT_URI
)

auth_router = APIRouter()

@auth_router.post("/register")
async def register(user_data: UserRegister):
    # Check if user already exists
    existing_user = await db.users.find_one({"email": user_data.email})
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Create new user
    user = User(
        email=user_data.email,
        name=user_data.name,
        password_hash=hash_password(user_data.password),
        age_verified=user_data.age_verified
    )
    
    # Insert user into database
    await db.users.insert_one(user.dict())
    
    # Create access token
    access_token = create_access_token(data={"sub": user.id})
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user.id,
            "email": user.email,
            "name": user.name,
            "age_verified": user.age_verified,
            "is_admin": user.is_admin
        }
    }

@auth_router.post("/login")
async def login(user_data: UserLogin):
    # Find user
    user_doc = await db.users.find_one({"email": user_data.email})
    if not user_doc or not verify_password(user_data.password, user_doc["password_hash"]):
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    user = User(**user_doc)
    access_token = create_access_token(data={"sub": user.id})
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user.id,
            "email": user.email,
            "name": user.name,
            "age_verified": user.age_verified,
            "is_admin": user.is_admin
        }
    }

@auth_router.get("/profile")
async def get_profile(current_user: User = Depends(get_current_user)):
    return {
        "user": {
            "id": current_user.id,
            "email": current_user.email,
            "name": current_user.name,
            "age_verified": current_user.age_verified,
            "is_admin": current_user.is_admin,
            "picture": current_user.picture
        }
    }

@auth_router.get("/google/login")
async def google_login(request: Request):
    redirect_uri = GOOGLE_REDIRECT_URI or str(request.url_for('google_callback'))
    return await oauth.google.authorize_redirect(request, redirect_uri)

@auth_router.get("/google/callback")
async def google_callback(request: Request):
    try:
        token = await oauth.google.authorize_access_token(request)
        user_info = token.get('userinfo')
        
        if not user_info:
            raise HTTPException(status_code=400, detail="Failed to get user info from Google")
        
        # Check if user exists
        existing_user = await db.users.find_one({"email": user_info['email']})
        
        if existing_user:
            user = User(**existing_user)
        else:
            # Create new user
            user = User(
                email=user_info['email'],
                name=user_info['name'],
                age_verified=True,  # Assume Google users are age verified
                picture=user_info.get('picture')
            )
            await db.users.insert_one(user.dict())
        
        # Create access token
        access_token = create_access_token(data={"sub": user.id})
        
        # Return HTML that posts message to parent window
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Login Success</title>
        </head>
        <body>
            <script>
                window.opener.postMessage({{
                    'token': '{access_token}',
                    'user': {{
                        'id': '{user.id}',
                        'email': '{user.email}',
                        'name': '{user.name}',
                        'age_verified': {str(user.age_verified).lower()},
                        'is_admin': {str(user.is_admin).lower()},
                        'picture': '{user.picture or ""}'
                    }}
                }}, '*');
                window.close();
            </script>
            <p>Login successful! This window should close automatically.</p>
        </body>
        </html>
        """
        
        return HTMLResponse(content=html_content)
        
    except Exception as e:
        # Return error HTML
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Login Error</title>
        </head>
        <body>
            <script>
                window.opener.postMessage({{
                    'error': 'Login failed: {str(e)}'
                }}, '*');
                window.close();
            </script>
            <p>Login failed: {str(e)}</p>
        </body>
        </html>
        """
        
        return HTMLResponse(content=html_content)

@auth_router.post("/google/token")
async def google_token_login(token_data: dict):
    try:
        # Verify the Google ID token
        id_info = id_token.verify_oauth2_token(
            token_data['token'], 
            google_requests.Request(), 
            GOOGLE_CLIENT_ID
        )
        
        # Check if user exists
        existing_user = await db.users.find_one({"email": id_info['email']})
        
        if existing_user:
            user = User(**existing_user)
        else:
            # Create new user
            user = User(
                email=id_info['email'],
                name=id_info['name'],
                age_verified=True,
                picture=id_info.get('picture')
            )
            await db.users.insert_one(user.dict())
        
        # Create access token
        access_token = create_access_token(data={"sub": user.id})
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "email": user.email,
                "name": user.name,
                "age_verified": user.age_verified,
                "is_admin": user.is_admin,
                "picture": user.picture
            }
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid token: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Authentication failed: {str(e)}")