from fastapi import APIRouter, HTTPException, Depends, File, UploadFile, Form, BackgroundTasks
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from fastapi.responses import StreamingResponse
from typing import List, Optional
import os
import uuid
from datetime import datetime
import aiofiles
import mimetypes
from pathlib import Path
from .index import (
    User, Video, get_current_user, get_admin_user, db
)

videos_router = APIRouter()
security = HTTPBearer(auto_error=False)

class VideoUpload:
    def __init__(self, title: str, description: str, category: str, tags: List[str] = []):
        self.title = title
        self.description = description
        self.category = category
        self.tags = tags

class VideoSearch:
    def __init__(self, query: Optional[str] = None, category: Optional[str] = None, 
                 tags: Optional[List[str]] = None, status: Optional[str] = None):
        self.query = query
        self.category = category
        self.tags = tags
        self.status = status

@videos_router.post("/upload")
async def upload_video(
    background_tasks: BackgroundTasks,
    title: str = Form(...),
    description: str = Form(...),
    category: str = Form(...),
    tags: str = Form(""),
    file: UploadFile = File(...),
    thumbnail: Optional[UploadFile] = File(None),
    current_user: User = Depends(get_current_user)
):
    # Note: File uploads in Vercel serverless functions have limitations
    # This is a simplified version - in production, you'd want to use cloud storage
    raise HTTPException(
        status_code=501, 
        detail="File uploads not supported in serverless environment. Please use cloud storage integration."
    )

@videos_router.get("/", response_model=List[dict])
async def get_videos(
    category: Optional[str] = None,
    status: Optional[str] = None,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
):
    # Build query
    query = {}
    
    # If user is not authenticated or not admin, only show approved videos
    if not credentials:
        query["status"] = "approved"
    else:
        try:
            # Try to get current user to check if admin
            from .index import verify_token
            payload = verify_token(credentials.credentials)
            user_id = payload.get("sub")
            user_doc = await db.users.find_one({"id": user_id})
            
            if not user_doc or not user_doc.get("is_admin", False):
                query["status"] = "approved"
            elif status:
                query["status"] = status
        except:
            query["status"] = "approved"
    
    if category:
        query["category"] = category
    
    # Get videos from database
    videos_cursor = db.videos.find(query).sort("created_at", -1).limit(50)
    videos = await videos_cursor.to_list(length=50)
    
    # Convert ObjectId to string and format response
    for video in videos:
        if "_id" in video:
            del video["_id"]
    
    return videos

@videos_router.get("/{video_id}")
async def get_video(
    video_id: str, 
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
):
    video_doc = await db.videos.find_one({"id": video_id})
    if not video_doc:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Check if user can access this video
    if video_doc["status"] != "approved":
        if not credentials:
            raise HTTPException(status_code=403, detail="Access denied")
        
        try:
            from .index import verify_token
            payload = verify_token(credentials.credentials)
            user_id = payload.get("sub")
            user_doc = await db.users.find_one({"id": user_id})
            
            # Allow access if user is admin or video uploader
            if not (user_doc and (user_doc.get("is_admin", False) or video_doc["uploaded_by"] == user_id)):
                raise HTTPException(status_code=403, detail="Access denied")
        except:
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Increment view count
    await db.videos.update_one(
        {"id": video_id},
        {"$inc": {"views": 1}}
    )
    
    if "_id" in video_doc:
        del video_doc["_id"]
    
    return video_doc

@videos_router.get("/{video_id}/stream")
async def stream_video(
    video_id: str, 
    token: Optional[str] = None, 
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
):
    # Note: Video streaming in serverless functions is not ideal
    # This would typically be handled by a CDN or cloud storage service
    raise HTTPException(
        status_code=501, 
        detail="Video streaming not supported in serverless environment. Please use CDN or cloud storage."
    )

@videos_router.get("/{video_id}/thumbnail")
async def get_thumbnail(
    video_id: str,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
):
    # Note: Similar to video streaming, thumbnails should be served from cloud storage
    raise HTTPException(
        status_code=501, 
        detail="Thumbnail serving not supported in serverless environment. Please use cloud storage."
    )

@videos_router.post("/{video_id}/approve")
async def approve_video(video_id: str, admin_user: User = Depends(get_admin_user)):
    result = await db.videos.update_one(
        {"id": video_id},
        {
            "$set": {
                "status": "approved",
                "approved_by": admin_user.id,
                "approved_at": datetime.utcnow()
            }
        }
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Video not found")
    
    return {"message": "Video approved successfully"}

@videos_router.post("/{video_id}/reject")
async def reject_video(video_id: str, admin_user: User = Depends(get_admin_user)):
    result = await db.videos.update_one(
        {"id": video_id},
        {
            "$set": {
                "status": "rejected",
                "approved_by": admin_user.id,
                "approved_at": datetime.utcnow()
            }
        }
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Video not found")
    
    return {"message": "Video rejected successfully"}

@videos_router.put("/{video_id}")
async def update_video(video_id: str, video_data: dict, admin_user: User = Depends(get_admin_user)):
    # Remove fields that shouldn't be updated
    update_data = {k: v for k, v in video_data.items() 
                   if k not in ["id", "uploaded_by", "created_at", "file_path", "filename"]}
    
    if not update_data:
        raise HTTPException(status_code=400, detail="No valid fields to update")
    
    result = await db.videos.update_one(
        {"id": video_id},
        {"$set": update_data}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Video not found")
    
    return {"message": "Video updated successfully"}

@videos_router.delete("/{video_id}")
async def delete_video(video_id: str, admin_user: User = Depends(get_admin_user)):
    video_doc = await db.videos.find_one({"id": video_id})
    if not video_doc:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Delete video from database
    await db.videos.delete_one({"id": video_id})
    
    # Note: In a real implementation, you'd also delete the actual video files
    # from cloud storage here
    
    return {"message": "Video deleted successfully"}

@videos_router.get("/categories/list")
async def get_categories():
    # Get unique categories from videos collection
    categories = await db.videos.distinct("category", {"status": "approved"})
    return {"categories": categories}

@videos_router.post("/search")
async def search_videos(
    search_data: dict, 
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
):
    query = {}
    
    # If user is not authenticated or not admin, only show approved videos
    if not credentials:
        query["status"] = "approved"
    else:
        try:
            from .index import verify_token
            payload = verify_token(credentials.credentials)
            user_id = payload.get("sub")
            user_doc = await db.users.find_one({"id": user_id})
            
            if not user_doc or not user_doc.get("is_admin", False):
                query["status"] = "approved"
            elif search_data.get("status"):
                query["status"] = search_data["status"]
        except:
            query["status"] = "approved"
    
    # Add search filters
    if search_data.get("query"):
        query["$or"] = [
            {"title": {"$regex": search_data["query"], "$options": "i"}},
            {"description": {"$regex": search_data["query"], "$options": "i"}},
            {"tags": {"$in": [search_data["query"]]}}
        ]
    
    if search_data.get("category"):
        query["category"] = search_data["category"]
    
    if search_data.get("tags"):
        query["tags"] = {"$in": search_data["tags"]}
    
    # Execute search
    videos_cursor = db.videos.find(query).sort("created_at", -1).limit(50)
    videos = await videos_cursor.to_list(length=50)
    
    # Clean up response
    for video in videos:
        if "_id" in video:
            del video["_id"]
    
    return {"videos": videos, "total": len(videos)}