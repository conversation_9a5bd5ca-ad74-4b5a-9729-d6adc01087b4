from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from motor.motor_asyncio import AsyncIOMotor<PERSON>lient
from pydantic import BaseModel
from typing import Optional
import os
import jwt
from datetime import datetime

# Environment variables
mongo_url = os.environ.get('MONGO_URL', 'mongodb://localhost:27017')
db_name = os.environ.get('DB_NAME', 'adult_content_platform')
JWT_SECRET = os.environ.get('JWT_SECRET', 'your-secret-key-here')

# MongoDB connection
client = AsyncIOMotorClient(mongo_url)
db = client[db_name]

app = FastAPI()
security = HTTPBearer()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class User(BaseModel):
    id: str
    email: str
    name: str
    password_hash: Optional[str] = None
    age_verified: bool = False
    is_admin: bool = False
    is_approved: bool = False
    created_at: datetime
    picture: Optional[str] = None
    session_token: Optional[str] = None

def verify_token(token: str):
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    token = credentials.credentials
    payload = verify_token(token)
    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    user_doc = await db.users.find_one({"id": user_id})
    if user_doc is None:
        raise HTTPException(status_code=401, detail="User not found")
    
    return User(**user_doc)

async def get_admin_user(current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")
    return current_user

@app.get("/")
async def get_users(admin_user: User = Depends(get_admin_user)):
    users_cursor = db.users.find({}).sort("created_at", -1)
    users = await users_cursor.to_list(length=None)
    
    # Remove sensitive information and clean up response
    for user in users:
        if "_id" in user:
            del user["_id"]
        if "password_hash" in user:
            del user["password_hash"]
        if "session_token" in user:
            del user["session_token"]
    
    return {"users": users}

@app.get("/stats")
async def get_admin_stats(admin_user: User = Depends(get_admin_user)):
    # Get various statistics for the admin dashboard
    total_users = await db.users.count_documents({})
    approved_users = await db.users.count_documents({"is_approved": True})
    pending_users = await db.users.count_documents({"is_approved": False})
    admin_users = await db.users.count_documents({"is_admin": True})
    
    total_videos = await db.videos.count_documents({})
    approved_videos = await db.videos.count_documents({"status": "approved"})
    pending_videos = await db.videos.count_documents({"status": "pending"})
    rejected_videos = await db.videos.count_documents({"status": "rejected"})
    
    return {
        "users": {
            "total": total_users,
            "approved": approved_users,
            "pending": pending_users,
            "admins": admin_users
        },
        "videos": {
            "total": total_videos,
            "approved": approved_videos,
            "pending": pending_videos,
            "rejected": rejected_videos
        }
    }

# Vercel handler
handler = app