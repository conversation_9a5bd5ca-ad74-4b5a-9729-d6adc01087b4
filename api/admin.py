from fastapi import APIRouter, HTTPException, Depends
from typing import List
from datetime import datetime
from .index import User, get_admin_user, db

admin_router = APIRouter()

@admin_router.get("/users")
async def get_users(admin_user: User = Depends(get_admin_user)):
    users_cursor = db.users.find({}).sort("created_at", -1)
    users = await users_cursor.to_list(length=None)
    
    # Remove sensitive information and clean up response
    for user in users:
        if "_id" in user:
            del user["_id"]
        if "password_hash" in user:
            del user["password_hash"]
        if "session_token" in user:
            del user["session_token"]
    
    return {"users": users}

@admin_router.post("/users/{user_id}/approve")
async def approve_user(user_id: str, admin_user: User = Depends(get_admin_user)):
    result = await db.users.update_one(
        {"id": user_id},
        {"$set": {"is_approved": True}}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    
    return {"message": "User approved successfully"}

@admin_router.post("/users/{user_id}/make-admin")
async def make_admin(user_id: str, admin_user: User = Depends(get_admin_user)):
    result = await db.users.update_one(
        {"id": user_id},
        {"$set": {"is_admin": True, "is_approved": True}}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    
    return {"message": "User promoted to admin successfully"}

@admin_router.get("/stats")
async def get_admin_stats(admin_user: User = Depends(get_admin_user)):
    # Get various statistics for the admin dashboard
    total_users = await db.users.count_documents({})
    approved_users = await db.users.count_documents({"is_approved": True})
    pending_users = await db.users.count_documents({"is_approved": False})
    admin_users = await db.users.count_documents({"is_admin": True})
    
    total_videos = await db.videos.count_documents({})
    approved_videos = await db.videos.count_documents({"status": "approved"})
    pending_videos = await db.videos.count_documents({"status": "pending"})
    rejected_videos = await db.videos.count_documents({"status": "rejected"})
    
    # Get recent activity
    recent_users = await db.users.find({}).sort("created_at", -1).limit(5).to_list(length=5)
    recent_videos = await db.videos.find({}).sort("created_at", -1).limit(5).to_list(length=5)
    
    # Clean up recent activity data
    for user in recent_users:
        if "_id" in user:
            del user["_id"]
        if "password_hash" in user:
            del user["password_hash"]
        if "session_token" in user:
            del user["session_token"]
    
    for video in recent_videos:
        if "_id" in video:
            del video["_id"]
    
    return {
        "users": {
            "total": total_users,
            "approved": approved_users,
            "pending": pending_users,
            "admins": admin_users
        },
        "videos": {
            "total": total_videos,
            "approved": approved_videos,
            "pending": pending_videos,
            "rejected": rejected_videos
        },
        "recent_activity": {
            "users": recent_users,
            "videos": recent_videos
        }
    }

@admin_router.get("/videos/pending")
async def get_pending_videos(admin_user: User = Depends(get_admin_user)):
    videos_cursor = db.videos.find({"status": "pending"}).sort("created_at", -1)
    videos = await videos_cursor.to_list(length=None)
    
    # Clean up response
    for video in videos:
        if "_id" in video:
            del video["_id"]
    
    return {"videos": videos}

@admin_router.delete("/users/{user_id}")
async def delete_user(user_id: str, admin_user: User = Depends(get_admin_user)):
    # Prevent admin from deleting themselves
    if user_id == admin_user.id:
        raise HTTPException(status_code=400, detail="Cannot delete your own account")
    
    # Check if user exists
    user_doc = await db.users.find_one({"id": user_id})
    if not user_doc:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Delete user
    await db.users.delete_one({"id": user_id})
    
    # Also delete all videos uploaded by this user
    await db.videos.delete_many({"uploaded_by": user_id})
    
    return {"message": "User and associated content deleted successfully"}

@admin_router.post("/users/{user_id}/revoke-admin")
async def revoke_admin(user_id: str, admin_user: User = Depends(get_admin_user)):
    # Prevent admin from revoking their own admin status
    if user_id == admin_user.id:
        raise HTTPException(status_code=400, detail="Cannot revoke your own admin status")
    
    result = await db.users.update_one(
        {"id": user_id},
        {"$set": {"is_admin": False}}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    
    return {"message": "Admin status revoked successfully"}

@admin_router.post("/users/{user_id}/suspend")
async def suspend_user(user_id: str, admin_user: User = Depends(get_admin_user)):
    # Prevent admin from suspending themselves
    if user_id == admin_user.id:
        raise HTTPException(status_code=400, detail="Cannot suspend your own account")
    
    result = await db.users.update_one(
        {"id": user_id},
        {"$set": {"is_approved": False, "suspended_at": datetime.utcnow()}}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    
    return {"message": "User suspended successfully"}