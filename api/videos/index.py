from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from motor.motor_asyncio import AsyncIOMotorClient
from pydantic import BaseModel, <PERSON>
from typing import List, Optional
import os
import jwt
from datetime import datetime
import uuid

# Environment variables
mongo_url = os.environ.get('MONGO_URL', 'mongodb://localhost:27017')
db_name = os.environ.get('DB_NAME', 'adult_content_platform')
JWT_SECRET = os.environ.get('JWT_SECRET', 'your-secret-key-here')

# MongoDB connection
client = AsyncIOMotorClient(mongo_url)
db = client[db_name]

app = FastAPI()
security = HTTPBearer(auto_error=False)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class User(BaseModel):
    id: str
    email: str
    name: str
    password_hash: Optional[str] = None
    age_verified: bool = False
    is_admin: bool = False
    is_approved: bool = False
    created_at: datetime
    picture: Optional[str] = None
    session_token: Optional[str] = None

class Video(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str
    description: str
    category: str
    tags: List[str] = []
    filename: str
    file_path: str
    file_size: int
    duration: Optional[int] = None
    thumbnail: Optional[str] = None
    uploaded_by: str
    status: str = "pending"
    created_at: datetime = Field(default_factory=datetime.utcnow)
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    views: int = 0

def verify_token(token: str):
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

@app.get("/")
async def get_videos(
    category: Optional[str] = None,
    status: Optional[str] = None,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
):
    # Build query
    query = {}
    
    # If user is not authenticated or not admin, only show approved videos
    if not credentials:
        query["status"] = "approved"
    else:
        try:
            # Try to get current user to check if admin
            payload = verify_token(credentials.credentials)
            user_id = payload.get("sub")
            user_doc = await db.users.find_one({"id": user_id})
            
            if not user_doc or not user_doc.get("is_admin", False):
                query["status"] = "approved"
            elif status:
                query["status"] = status
        except:
            query["status"] = "approved"
    
    if category:
        query["category"] = category
    
    # Get videos from database
    videos_cursor = db.videos.find(query).sort("created_at", -1).limit(50)
    videos = await videos_cursor.to_list(length=50)
    
    # Convert ObjectId to string and format response
    for video in videos:
        if "_id" in video:
            del video["_id"]
    
    return {"videos": videos}

@app.get("/categories")
async def get_categories():
    # Get unique categories from videos collection
    categories = await db.videos.distinct("category", {"status": "approved"})
    return {"categories": categories}

@app.post("/search")
async def search_videos(
    search_data: dict, 
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
):
    query = {}
    
    # If user is not authenticated or not admin, only show approved videos
    if not credentials:
        query["status"] = "approved"
    else:
        try:
            payload = verify_token(credentials.credentials)
            user_id = payload.get("sub")
            user_doc = await db.users.find_one({"id": user_id})
            
            if not user_doc or not user_doc.get("is_admin", False):
                query["status"] = "approved"
            elif search_data.get("status"):
                query["status"] = search_data["status"]
        except:
            query["status"] = "approved"
    
    # Add search filters
    if search_data.get("query"):
        query["$or"] = [
            {"title": {"$regex": search_data["query"], "$options": "i"}},
            {"description": {"$regex": search_data["query"], "$options": "i"}},
            {"tags": {"$in": [search_data["query"]]}}
        ]
    
    if search_data.get("category"):
        query["category"] = search_data["category"]
    
    if search_data.get("tags"):
        query["tags"] = {"$in": search_data["tags"]}
    
    # Execute search
    videos_cursor = db.videos.find(query).sort("created_at", -1).limit(50)
    videos = await videos_cursor.to_list(length=50)
    
    # Clean up response
    for video in videos:
        if "_id" in video:
            del video["_id"]
    
    return {"videos": videos, "total": len(videos)}

# Vercel handler
handler = app