# Google OAuth Setup Instructions

## Overview
The authentication system has been successfully migrated from Emergent to Google OAuth. This provides a more standard and reliable authentication flow.

## What Changed

### Backend Changes
1. **Removed Emergent Authentication**: Replaced `/auth/emergent-login` endpoint
2. **Added Google OAuth Endpoints**:
   - `GET /api/auth/google/login` - Initiates Google OAuth flow
   - `GET /api/auth/google/callback` - Handles OAuth callback
   - `POST /api/auth/google/token` - Verifies Google ID tokens

3. **New Dependencies**: Added Google OAuth libraries
   - `google-auth>=2.23.0`
   - `google-auth-oauthlib>=1.1.0`
   - `google-auth-httplib2>=0.2.0`

### Frontend Changes
1. **Replaced Emergent Login**: Updated `handleEmergentLogin()` to `handleGoogleLogin()`
2. **Popup-based Authentication**: Uses popup window for OAuth flow
3. **Simplified Callback**: Removed complex session ID handling

## Setup Google OAuth Credentials

### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API or Google Identity API

### Step 2: Configure OAuth Consent Screen
1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type
3. Fill in required information:
   - App name: "AdultFlix"
   - User support email: Your email
   - Developer contact: Your email

### Step 3: Create OAuth Credentials
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Web application"
4. Configure:
   - **Authorized JavaScript origins**: `http://localhost:3000`
   - **Authorized redirect URIs**: `http://localhost:8001/api/auth/google/callback`

### Step 4: Update Environment Variables
Update your `backend/.env` file with the credentials:

```env
GOOGLE_CLIENT_ID="your-actual-google-client-id-here"
GOOGLE_CLIENT_SECRET="your-actual-google-client-secret-here"
GOOGLE_REDIRECT_URI="http://localhost:8001/api/auth/google/callback"
```

## Testing the Implementation

1. **Start the Backend**: The server should be running on `http://localhost:8001`
2. **Start the Frontend**: The React app should be running on `http://localhost:3000`
3. **Test Google Login**: Click "Continue with Google" button
4. **Verify Flow**: Should open popup, authenticate with Google, and return to app

## Authentication Flow

1. User clicks "Continue with Google"
2. Frontend opens popup to `/api/auth/google/login`
3. Backend redirects to Google OAuth
4. User authenticates with Google
5. Google redirects to `/api/auth/google/callback`
6. Backend processes OAuth response and creates user
7. Backend sends JavaScript to popup with token and user data
8. Frontend receives message and logs user in
9. Popup closes automatically

## Benefits of Google OAuth

- **Standard Implementation**: Uses widely-adopted OAuth 2.0 standard
- **Better Security**: Leverages Google's security infrastructure
- **User Trust**: Users are familiar with Google authentication
- **No Third-party Dependencies**: Removes dependency on Emergent service
- **Better Error Handling**: More predictable error responses

## Troubleshooting

### Common Issues
1. **"Invalid Client" Error**: Check GOOGLE_CLIENT_ID in .env
2. **"Redirect URI Mismatch"**: Verify redirect URI in Google Console matches .env
3. **CORS Errors**: Ensure frontend origin is in authorized JavaScript origins
4. **Popup Blocked**: Browser may block popup - user needs to allow popups

### Development vs Production
- **Development**: Uses `localhost` URLs
- **Production**: Update redirect URIs and origins to production domains
- **HTTPS Required**: Production must use HTTPS for Google OAuth