* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0a0a0a;
  color: #ffffff;
}

.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
}

/* Header Styles */
.header {
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #333;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 2rem;
  font-weight: bold;
  color: #e50914;
  text-shadow: 0 0 10px rgba(229, 9, 20, 0.5);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Hero Section */
.hero {
  position: relative;
  height: 15vh;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.3;
}

.hero-content {
  text-align: center;
  z-index: 1;
  max-width: 600px;
  padding: 0.5rem;
}

.hero-content h2 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.hero-content p {
  font-size: 1rem;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  margin: 0;
}

/* Search Section */
.search-section {
  padding: 2rem;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid #333;
}

.search-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.search-bar {
  flex: 1;
  display: flex;
  gap: 0.5rem;
}

.search-bar input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #555;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 1rem;
}

.search-bar input:focus {
  outline: none;
  border-color: #e50914;
}

.filter-bar select {
  padding: 0.75rem;
  border: 1px solid #555;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 1rem;
}

/* Video Grid */
.video-grid {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.videos-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.video-card {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  border: 1px solid #333;
}

.video-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(229, 9, 20, 0.3);
}

.video-thumbnail {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-card:hover .play-overlay {
  opacity: 1;
}

.play-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(229, 9, 20, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #fff;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.video-info {
  padding: 1rem;
}

.video-info h3 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: #fff;
}

.video-info p {
  color: #ccc;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.category {
  background: #e50914;
  color: #fff;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.views {
  color: #999;
  font-size: 0.8rem;
}

.video-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag {
  background: rgba(255, 255, 255, 0.1);
  color: #ccc;
  padding: 0.2rem 0.4rem;
  border-radius: 8px;
  font-size: 0.7rem;
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #e50914;
  color: #fff;
}

.btn-primary:hover {
  background: #f40612;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(229, 9, 20, 0.4);
}

.btn-secondary {
  background: #333;
  color: #fff;
}

.btn-secondary:hover {
  background: #555;
}

.btn-google {
  background: #4285f4;
  color: #fff;
}

.btn-google:hover {
  background: #357ae8;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Auth Styles */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  padding: 2rem;
}

.auth-card {
  background: rgba(0, 0, 0, 0.9);
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #333;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.auth-card h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #e50914;
}

.auth-card input {
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 1rem;
  border: 1px solid #555;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 1rem;
}

.auth-card input:focus {
  outline: none;
  border-color: #e50914;
}

.auth-divider {
  text-align: center;
  margin: 1.5rem 0;
  position: relative;
  color: #666;
}

.auth-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #333;
}

.auth-divider span {
  background: rgba(0, 0, 0, 0.9);
  padding: 0 1rem;
}

.auth-switch {
  text-align: center;
  margin-top: 1.5rem;
  color: #ccc;
}

.link-button {
  background: none;
  border: none;
  color: #e50914;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
  margin-left: 0.5rem;
}

.link-button:hover {
  color: #f40612;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: rgba(0, 0, 0, 0.95);
  padding: 2rem;
  border-radius: 8px;
  border: 1px solid #333;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-content h2 {
  margin-bottom: 1rem;
  color: #e50914;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.upload-modal {
  max-width: 600px;
}

.upload-modal input,
.upload-modal textarea {
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 1rem;
  border: 1px solid #555;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 1rem;
}

.upload-modal textarea {
  resize: vertical;
  min-height: 100px;
}

.upload-modal input:focus,
.upload-modal textarea:focus {
  outline: none;
  border-color: #e50914;
}

.file-label {
  color: #ccc;
  font-size: 0.9rem;
  margin-top: -0.5rem;
  margin-bottom: 0.5rem;
}

.upload-modal input[type="file"] {
  padding: 0.5rem;
  border: 2px dashed #555;
  background: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.upload-modal input[type="file"]:hover {
  border-color: #e50914;
}

/* Video Player */
.video-player-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.video-player-content {
  position: relative;
  width: 90%;
  max-width: 1000px;
  height: 70vh;
}

.close-button {
  position: absolute;
  top: -40px;
  right: 0;
  background: none;
  border: none;
  color: #fff;
  font-size: 2rem;
  cursor: pointer;
  z-index: 1001;
}

.video-player {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

/* Loading and Error States */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  font-size: 1.2rem;
  color: #ccc;
}

.error-message {
  background: rgba(220, 53, 69, 0.2);
  color: #ff6b6b;
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  text-align: center;
  padding: 2rem;
}

.error-container h2 {
  color: #e50914;
  margin-bottom: 1rem;
}

.no-videos {
  text-align: center;
  padding: 4rem 2rem;
  color: #ccc;
}

.no-videos h3 {
  margin-bottom: 0.5rem;
}

/* Approval Banner */
.approval-banner {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 193, 7, 0.9);
  color: #000;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1000;
  font-weight: 500;
}

.approval-banner button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #000;
}

/* Admin Actions */
.admin-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  border-radius: 4px;
}

.btn-success {
  background: #28a745;
  color: white;
  border: 1px solid #28a745;
}

.btn-success:hover {
  background: #218838;
  border-color: #1e7e34;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: 1px solid #dc3545;
}

.btn-danger:hover {
  background: #c82333;
  border-color: #bd2130;
}

/* Video Status */
.video-status {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: bold;
  text-transform: uppercase;
}

.video-status.pending {
  background: rgba(255, 193, 7, 0.9);
  color: #000;
}

.video-status.rejected {
  background: rgba(220, 53, 69, 0.9);
  color: #fff;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 1rem;
  }
  
  .hero-content h2 {
    font-size: 2rem;
  }
  
  .search-container {
    flex-direction: column;
  }
  
  .videos-container {
    grid-template-columns: 1fr;
  }
  
  .video-player-content {
    width: 95%;
    height: 60vh;
  }
  
  .auth-card {
    margin: 1rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .hero-content h2 {
    font-size: 1.5rem;
  }
  
  .hero-content p {
    font-size: 1rem;
  }
  
  .modal-content {
    width: 95%;
    padding: 1rem;
  }
}

/* Admin Page Styles */
.admin-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #333;
}

.admin-header h2 {
  color: #e50914;
  font-size: 2rem;
}

.admin-header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Admin Tabs */
.admin-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid #333;
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 2rem;
  color: #ccc;
  cursor: pointer;
  font-size: 1rem;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #fff;
  background: rgba(255, 255, 255, 0.05);
}

.tab-button.active {
  color: #e50914;
  border-bottom-color: #e50914;
}

/* User Management Styles */
.admin-users-section {
  margin-top: 1rem;
}

.admin-users-section h3 {
  color: #e50914;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.users-table {
  width: 100%;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333;
}

.users-table table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th,
.users-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #333;
}

.users-table th {
  background: rgba(229, 9, 20, 0.1);
  color: #e50914;
  font-weight: 600;
}

.users-table td {
  color: #fff;
}

.users-table tr:hover {
  background: rgba(255, 255, 255, 0.05);
}

.users-table .btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
  margin-right: 0.5rem;
}

.btn-success {
  background: #28a745;
  border-color: #28a745;
}

.btn-success:hover {
  background: #218838;
  border-color: #1e7e34;
}

.no-users {
  text-align: center;
  padding: 2rem;
  color: #ccc;
}

/* Public App Styles */
.hero-cta {
  margin-top: 1rem;
  padding: 12px 24px;
  font-size: 1.1rem;
}

/* Auth Modal Styles */
.auth-modal {
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 15px;
  right: 20px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  z-index: 1;
}

.modal-close:hover {
  color: #000;
}

/* Sign-in Prompt Styles */
.sign-in-prompt {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
}

.prompt-content h3 {
  margin: 0 0 10px 0;
  color: #ff6b6b;
}

.prompt-content p {
  margin: 0 0 15px 0;
  font-size: 14px;
}

.prompt-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.prompt-actions .btn {
  padding: 8px 16px;
  font-size: 14px;
}

/* Responsive adjustments for public components */
@media (max-width: 768px) {
  .hero-cta {
    font-size: 1rem;
    padding: 10px 20px;
  }
  
  .auth-modal {
    width: 95%;
    margin: 20px;
  }
  
  .sign-in-prompt {
    bottom: 10px;
    left: 10px;
    right: 10px;
    padding: 15px;
  }
  
  .prompt-actions {
    flex-direction: column;
  }
  
  .prompt-actions .btn {
    width: 100%;
  }
}

.admin-videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.admin-video-card {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.admin-video-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(229, 9, 20, 0.2);
}

.admin-video-card .video-thumbnail {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.admin-video-card .video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.admin-video-card .video-info {
  padding: 1rem;
}

.admin-video-card .video-info h3 {
  margin-bottom: 0.5rem;
  color: #fff;
  font-size: 1.1rem;
}

.admin-video-card .video-info p {
  color: #ccc;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.admin-video-card .video-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  color: #999;
}

.admin-video-card .video-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.admin-video-card .tag {
  background: rgba(229, 9, 20, 0.2);
  color: #e50914;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  border: 1px solid rgba(229, 9, 20, 0.3);
}

.admin-video-card .admin-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.admin-video-card .admin-actions .btn {
  flex: 1;
  padding: 0.5rem;
  font-size: 0.9rem;
}

/* Edit Video Modal */
.edit-video-modal {
  max-width: 500px;
  width: 90%;
}

.edit-video-modal h2 {
  color: #e50914;
  margin-bottom: 1.5rem;
  text-align: center;
}

.edit-video-modal form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.edit-video-modal input,
.edit-video-modal textarea {
  padding: 0.75rem;
  border: 1px solid #555;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 1rem;
  font-family: inherit;
}

.edit-video-modal input:focus,
.edit-video-modal textarea:focus {
  outline: none;
  border-color: #e50914;
  box-shadow: 0 0 0 2px rgba(229, 9, 20, 0.2);
}

.edit-video-modal textarea {
  resize: vertical;
  min-height: 100px;
}

.edit-video-modal .modal-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.edit-video-modal .modal-actions .btn {
  flex: 1;
  padding: 0.75rem;
  font-size: 1rem;
}

/* Admin responsive design */
@media (max-width: 768px) {
  .admin-page {
    padding: 1rem;
  }
  
  .admin-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .admin-videos-grid {
    grid-template-columns: 1fr;
  }
  
  .edit-video-modal {
    width: 95%;
  }
  
  .edit-video-modal .modal-actions {
    flex-direction: column;
  }
}