import React, { useState, useEffect, createContext, useContext } from 'react';
import './App.css';
import axios from 'axios';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL;
const API = `${BACKEND_URL}/api`;

// Auth Context
const AuthContext = createContext();

const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      fetchProfile();
    } else {
      setLoading(false);
    }
  }, [token]);

  const fetchProfile = async () => {
    try {
      const response = await axios.get(`${API}/auth/profile`);
      setUser(response.data);
    } catch (error) {
      console.error('Failed to fetch profile:', error);
      logout();
    } finally {
      setLoading(false);
    }
  };

  const login = (token, userData) => {
    localStorage.setItem('token', token);
    setToken(token);
    setUser(userData);
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  };

  const logout = () => {
    localStorage.removeItem('token');
    setToken(null);
    setUser(null);
    delete axios.defaults.headers.common['Authorization'];
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Age Verification Modal
const AgeVerificationModal = ({ isOpen, onVerify, onCancel }) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <h2>Age Verification Required</h2>
        <p>You must be 18 or older to access this content.</p>
        <p>By clicking "I am 18+", you confirm that you are of legal age to view adult content.</p>
        <div className="modal-actions">
          <button onClick={onVerify} className="btn btn-primary">I am 18+</button>
          <button onClick={onCancel} className="btn btn-secondary">Cancel</button>
        </div>
      </div>
    </div>
  );
};

// Auth Forms
const AuthForms = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: '',
    age_verified: false
  });
  const [showAgeVerification, setShowAgeVerification] = useState(false);
  const [error, setError] = useState('');
  const { login } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!isLogin && !formData.age_verified) {
      setShowAgeVerification(true);
      return;
    }

    try {
      const endpoint = isLogin ? '/auth/login' : '/auth/register';
      const response = await axios.post(`${API}${endpoint}`, formData);
      
      login(response.data.access_token, response.data.user);
    } catch (error) {
      // Handle different error response formats
      let errorMessage = 'Authentication failed';
      if (error.response?.data?.detail) {
        if (Array.isArray(error.response.data.detail)) {
          // Handle FastAPI validation errors
          errorMessage = error.response.data.detail.map(err => err.msg).join(', ');
        } else if (typeof error.response.data.detail === 'string') {
          errorMessage = error.response.data.detail;
        } else {
          errorMessage = JSON.stringify(error.response.data.detail);
        }
      }
      setError(errorMessage);
    }
  };

  const handleAgeVerification = (verified) => {
    setShowAgeVerification(false);
    if (verified) {
      setFormData({ ...formData, age_verified: true });
    }
  };

  const handleGoogleLogin = () => {
    const popup = window.open(
      `${API}/auth/google/login`,
      'google-login',
      'width=500,height=600,scrollbars=yes,resizable=yes'
    );
    
    const handleMessage = (event) => {
      // Allow messages from the backend server
      if (event.origin !== window.location.origin && event.origin !== BACKEND_URL) return;
      
      if (event.data.token && event.data.user) {
        login(event.data.token, event.data.user);
        if (popup && !popup.closed) {
          popup.close();
        }
        window.removeEventListener('message', handleMessage);
      }
    };
    
    window.addEventListener('message', handleMessage);
    
    // Check if popup was closed manually
    const checkClosed = setInterval(() => {
      if (popup && popup.closed) {
        clearInterval(checkClosed);
        window.removeEventListener('message', handleMessage);
      }
    }, 1000);
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <h2>{isLogin ? 'Sign In' : 'Create Account'}</h2>
        
        {error && <div className="error-message">{error}</div>}
        
        <form onSubmit={handleSubmit}>
          {!isLogin && (
            <input
              type="text"
              placeholder="Full Name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
          )}
          <input
            type="email"
            placeholder="Email"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            required
          />
          <input
            type="password"
            placeholder="Password"
            value={formData.password}
            onChange={(e) => setFormData({ ...formData, password: e.target.value })}
            required
          />
          <button type="submit" className="btn btn-primary">
            {isLogin ? 'Sign In' : 'Create Account'}
          </button>
        </form>

        <div className="auth-divider">
          <span>or</span>
        </div>

        <button onClick={handleGoogleLogin} className="btn btn-google">
          Continue with Google
        </button>

        <p className="auth-switch">
          {isLogin ? "Don't have an account?" : "Already have an account?"}
          <button onClick={() => setIsLogin(!isLogin)} className="link-button">
            {isLogin ? 'Sign Up' : 'Sign In'}
          </button>
        </p>
      </div>

      <AgeVerificationModal
        isOpen={showAgeVerification}
        onVerify={() => handleAgeVerification(true)}
        onCancel={() => handleAgeVerification(false)}
      />
    </div>
  );
};

// Video Player Component
const VideoPlayer = ({ videoId, onClose }) => {
  const [videoUrl, setVideoUrl] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('token');
    const url = `${API}/videos/${videoId}/stream?token=${encodeURIComponent(token)}`;
    setVideoUrl(url);
    setLoading(false);
  }, [videoId]);

  return (
    <div className="video-player-modal">
      <div className="video-player-content">
        <button className="close-button" onClick={onClose}>×</button>
        {loading ? (
          <div className="loading">Loading video...</div>
        ) : (
          <video controls autoPlay className="video-player">
            <source src={videoUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        )}
      </div>
    </div>
  );
};

// Video Card Component
const VideoCard = ({ video, onPlay, onApprove, onReject, isAdmin }) => {
  const handleCardClick = (e) => {
    // Don't trigger video play if clicking on admin buttons
    if (e.target.closest('.admin-actions')) {
      return;
    }
    onPlay(video.id);
  };

  const thumbnailUrl = video.thumbnail 
    ? `${API}/videos/${video.id}/thumbnail`
    : "https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=300&h=200&fit=crop";

  return (
    <div className="video-card" onClick={handleCardClick}>
      <div className="video-thumbnail">
        <img 
          src={thumbnailUrl}
          alt={video.title}
        />
        <div className="play-overlay">
          <div className="play-button">▶</div>
        </div>
        {video.status === 'pending' && (
          <div className="video-status pending">Pending</div>
        )}
        {video.status === 'rejected' && (
          <div className="video-status rejected">Rejected</div>
        )}
      </div>
      <div className="video-info">
        <h3>{video.title}</h3>
        <p>{video.description}</p>
        <div className="video-meta">
          <span className="category">{video.category}</span>
          <span className="views">{video.views} views</span>
        </div>
        <div className="video-tags">
          {video.tags.map(tag => (
            <span key={tag} className="tag">{tag}</span>
          ))}
        </div>
        {isAdmin && video.status === 'pending' && (
          <div className="admin-actions">
            <button 
              onClick={() => onApprove(video.id)} 
              className="btn btn-success btn-sm"
            >
              Approve
            </button>
            <button 
              onClick={() => onReject(video.id)} 
              className="btn btn-danger btn-sm"
            >
              Reject
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// Admin Page Component
const AdminPage = ({ onBack, onShowUpload }) => {
  const [adminVideos, setAdminVideos] = useState([]);
  const [editingVideo, setEditingVideo] = useState(null);
  const [users, setUsers] = useState([]);
  const [activeTab, setActiveTab] = useState('videos');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (activeTab === 'videos') {
      fetchAdminVideos();
    } else if (activeTab === 'users') {
      fetchUsers();
    }
  }, [activeTab]);

  const fetchAdminVideos = async () => {
    try {
      const response = await axios.get(`${API}/videos`);
      setAdminVideos(response.data);
    } catch (error) {
      console.error('Failed to fetch videos:', error);
      setError('Failed to load videos');
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API}/admin/users`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setUsers(response.data);
    } catch (error) {
      console.error('Failed to fetch users:', error);
      setError('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleMakeAdmin = async (userId) => {
    if (!window.confirm('Are you sure you want to grant admin access to this user?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      await axios.post(`${API}/admin/users/${userId}/make-admin`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });
      fetchUsers(); // Refresh the user list
      alert('User granted admin access successfully');
    } catch (error) {
      console.error('Failed to make user admin:', error);
      alert('Failed to grant admin access');
    }
  };

  const handleApproveUser = async (userId) => {
    try {
      const token = localStorage.getItem('token');
      await axios.post(`${API}/admin/users/${userId}/approve`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });
      fetchUsers(); // Refresh the user list
      alert('User approved successfully');
    } catch (error) {
      console.error('Failed to approve user:', error);
      alert('Failed to approve user');
    }
  };

  const handleEditVideo = (video) => {
    setEditingVideo(video);
  };

  const handleUpdateVideo = async (videoId, updateData) => {
    try {
      await axios.put(`${API}/videos/${videoId}`, updateData);
      setEditingVideo(null);
      fetchAdminVideos();
    } catch (error) {
      console.error('Failed to update video:', error);
      alert('Failed to update video');
    }
  };

  const handleDeleteVideo = async (videoId) => {
    if (!window.confirm('Are you sure you want to delete this video? This action cannot be undone.')) {
      return;
    }

    try {
      await axios.delete(`${API}/videos/${videoId}`);
      fetchAdminVideos();
    } catch (error) {
      console.error('Failed to delete video:', error);
      alert('Failed to delete video');
    }
  };

  if (loading) {
    return <div className="loading">Loading admin panel...</div>;
  }

  return (
      <div className="admin-page">
        <div className="admin-header">
          <h2>Admin Panel</h2>
          <div className="admin-header-actions">
            {activeTab === 'videos' && (
              <button onClick={onShowUpload} className="btn btn-primary">
                Upload Video
              </button>
            )}
            <button onClick={onBack} className="btn btn-secondary">Back to Home</button>
          </div>
        </div>

        <div className="admin-tabs">
          <button 
            className={`tab-button ${activeTab === 'videos' ? 'active' : ''}`}
            onClick={() => setActiveTab('videos')}
          >
            Video Management
          </button>
          <button 
            className={`tab-button ${activeTab === 'users' ? 'active' : ''}`}
            onClick={() => setActiveTab('users')}
          >
            User Management
          </button>
        </div>

        {error && <div className="error-message">{error}</div>}

        {activeTab === 'videos' ? (
          <div className="admin-videos-grid">
            {adminVideos.length === 0 ? (
              <div className="no-videos">
                <h3>No videos found</h3>
              </div>
            ) : (
              adminVideos.map(video => (
                <AdminVideoCard
                  key={video.id}
                  video={video}
                  onEdit={handleEditVideo}
                  onDelete={handleDeleteVideo}
                />
              ))
            )}
          </div>
        ) : (
          <div className="admin-users-section">
            <h3>All Users</h3>
            <div className="users-table">
              {users.length === 0 ? (
                <div className="no-users">
                  <p>No users found</p>
                </div>
              ) : (
                <table className="users-table">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Email</th>
                      <th>Admin</th>
                      <th>Approved</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map(user => (
                      <tr key={user.id}>
                        <td>{user.name}</td>
                        <td>{user.email}</td>
                        <td>{user.is_admin ? 'Yes' : 'No'}</td>
                        <td>{user.is_approved ? 'Yes' : 'No'}</td>
                        <td>
                          {!user.is_admin && (
                            <button 
                              onClick={() => handleMakeAdmin(user.id)}
                              className="btn btn-primary btn-sm"
                            >
                              Make Admin
                            </button>
                          )}
                          {!user.is_approved && (
                            <button 
                              onClick={() => handleApproveUser(user.id)}
                              className="btn btn-success btn-sm"
                              style={{marginLeft: '5px'}}
                            >
                              Approve
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        )}

        {editingVideo && (
          <EditVideoModal
            video={editingVideo}
            onClose={() => setEditingVideo(null)}
            onUpdate={handleUpdateVideo}
          />
        )}
      </div>
  );
};

// Admin Video Card Component
const AdminVideoCard = ({ video, onEdit, onDelete }) => {
  const thumbnailUrl = video.thumbnail 
    ? `${API}/videos/${video.id}/thumbnail`
    : "https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=300&h=200&fit=crop";

  return (
    <div className="admin-video-card">
      <div className="video-thumbnail">
        <img 
          src={thumbnailUrl}
          alt={video.title}
        />
        <div className={`video-status ${video.status}`}>{video.status}</div>
      </div>
      <div className="video-info">
        <h3>{video.title}</h3>
        <p>{video.description}</p>
        <div className="video-meta">
          <span className="category">{video.category}</span>
          <span className="views">{video.views} views</span>
        </div>
        <div className="video-tags">
          {video.tags.map(tag => (
            <span key={tag} className="tag">{tag}</span>
          ))}
        </div>
        <div className="admin-actions">
          <button 
            onClick={() => onEdit(video)} 
            className="btn btn-primary btn-sm"
          >
            Edit
          </button>
          <button 
            onClick={() => onDelete(video.id)} 
            className="btn btn-danger btn-sm"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

// Edit Video Modal Component
const EditVideoModal = ({ video, onClose, onUpdate }) => {
  const [formData, setFormData] = useState({
    title: video.title,
    description: video.description,
    category: video.category,
    tags: video.tags.join(', ')
  });
  const [saving, setSaving] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    const updateData = {
      ...formData,
      tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
    };

    await onUpdate(video.id, updateData);
    setSaving(false);
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content edit-video-modal">
        <h2>Edit Video</h2>
        
        <form onSubmit={handleSubmit}>
          <input
            type="text"
            placeholder="Video Title"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            required
          />
          <textarea
            placeholder="Video Description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            rows="4"
            required
          />
          <input
            type="text"
            placeholder="Category"
            value={formData.category}
            onChange={(e) => setFormData({ ...formData, category: e.target.value })}
            required
          />
          <input
            type="text"
            placeholder="Tags (comma separated)"
            value={formData.tags}
            onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
          />
          
          <div className="modal-actions">
            <button type="submit" className="btn btn-primary" disabled={saving}>
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
            <button type="button" onClick={onClose} className="btn btn-secondary">
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Upload Form Component
const UploadForm = ({ onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    tags: ''
  });
  const [file, setFile] = useState(null);
  const [thumbnail, setThumbnail] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file) {
      setError('Please select a video file');
      return;
    }

    setUploading(true);
    setError('');

    const uploadData = new FormData();
    uploadData.append('title', formData.title);
    uploadData.append('description', formData.description);
    uploadData.append('category', formData.category);
    uploadData.append('tags', formData.tags);
    uploadData.append('file', file);
    if (thumbnail) {
      uploadData.append('thumbnail', thumbnail);
    }

    try {
      await axios.post(`${API}/videos/upload`, uploadData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      onSuccess();
    } catch (error) {
      // Handle different error response formats
      let errorMessage = 'Upload failed';
      if (error.response?.data?.detail) {
        if (Array.isArray(error.response.data.detail)) {
          // Handle FastAPI validation errors
          errorMessage = error.response.data.detail.map(err => err.msg).join(', ');
        } else if (typeof error.response.data.detail === 'string') {
          errorMessage = error.response.data.detail;
        } else {
          errorMessage = JSON.stringify(error.response.data.detail);
        }
      }
      setError(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content upload-modal">
        <h2>Upload Video</h2>
        {error && <div className="error-message">{error}</div>}
        
        <form onSubmit={handleSubmit}>
          <input
            type="text"
            placeholder="Video Title"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            required
          />
          <textarea
            placeholder="Video Description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            rows="4"
            required
          />
          <input
            type="text"
            placeholder="Category"
            value={formData.category}
            onChange={(e) => setFormData({ ...formData, category: e.target.value })}
            required
          />
          <input
            type="text"
            placeholder="Tags (comma separated)"
            value={formData.tags}
            onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
          />
          <input
            type="file"
            accept="video/*"
            onChange={(e) => setFile(e.target.files[0])}
            required
          />
          <input
            type="file"
            accept="image/*"
            onChange={(e) => setThumbnail(e.target.files[0])}
            placeholder="Thumbnail (optional)"
          />
          <label htmlFor="thumbnail" className="file-label">Thumbnail (optional)</label>
          
          <div className="modal-actions">
            <button type="submit" disabled={uploading} className="btn btn-primary">
              {uploading ? 'Uploading...' : 'Upload Video'}
            </button>
            <button type="button" onClick={onClose} className="btn btn-secondary">
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Main App Component
const MainApp = () => {
  const { user, logout } = useAuth();
  const [videos, setVideos] = useState([]);
  const [categories, setCategories] = useState([]);
  const [currentVideo, setCurrentVideo] = useState(null);
  const [showUpload, setShowUpload] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState('home');

  useEffect(() => {
    fetchVideos();
    fetchCategories();
  }, [selectedCategory]);

  const fetchVideos = async () => {
    try {
      const params = selectedCategory ? { category: selectedCategory } : {};
      const response = await axios.get(`${API}/videos`, { params });
      setVideos(response.data);
    } catch (error) {
      console.error('Failed to fetch videos:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get(`${API}/categories`);
      setCategories(response.data || []);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      setCategories([]);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      fetchVideos();
      return;
    }

    try {
      const response = await axios.post(`${API}/search`, {
        query: searchQuery,
        category: selectedCategory || null
      });
      setVideos(response.data);
    } catch (error) {
      console.error('Search failed:', error);
    }
  };

  const handleUploadSuccess = () => {
    setShowUpload(false);
    if (currentPage === 'admin') {
      // If on admin page, we'll need to refresh admin videos
      // This will be handled by the AdminPage component's useEffect
      window.location.reload(); // Simple refresh for now
    } else {
      fetchVideos(); // Refresh the video list for home page
    }
  };

  const handleApproveVideo = async (videoId) => {
    try {
      await axios.post(`${API}/videos/${videoId}/approve`);
      fetchVideos(); // Refresh the video list
    } catch (error) {
      console.error('Failed to approve video:', error);
      alert('Failed to approve video');
    }
  };

  const handleRejectVideo = async (videoId) => {
    try {
      await axios.post(`${API}/videos/${videoId}/reject`);
      fetchVideos(); // Refresh the video list
    } catch (error) {
      console.error('Failed to reject video:', error);
      alert('Failed to reject video');
    }
  };

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  return (
    <div className="app">
      {/* Header */}
      <header className="header">
        <div className="header-content">
          <h1 className="logo" onClick={() => setCurrentPage('home')} style={{cursor: 'pointer'}}>Bluefilmx</h1>
          <div className="header-actions">
            {user?.is_admin && (
              <button 
                onClick={() => setCurrentPage('admin')} 
                className={`btn ${currentPage === 'admin' ? 'btn-primary' : 'btn-secondary'}`}
              >
                Admin Panel
              </button>
            )}
            <div className="user-menu">
              <span>Welcome, {user?.name}</span>
              <button onClick={logout} className="btn btn-secondary">Logout</button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      {currentPage === 'admin' ? (
        <AdminPage 
          onBack={() => setCurrentPage('home')} 
          onShowUpload={() => setShowUpload(true)}
        />
      ) : (
        <>
          {/* Hero Section */}
          <section className="hero">
            <div className="hero-content">
              <h2>Premium Adult Entertainment</h2>
              <p>Stream high-quality content in a safe, secure environment</p>
            </div>
            <div className="hero-image">
              <img src="https://images.unsplash.com/photo-1717295248358-4b8f2c8989d6?w=1200&h=400&fit=crop" alt="Premium streaming" />
            </div>
          </section>

          {/* Search and Filter */}
          <section className="search-section">
            <div className="search-container">
              <div className="search-bar">
                <input
                  type="text"
                  placeholder="Search videos..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
                <button onClick={handleSearch} className="btn btn-primary">Search</button>
              </div>
              <div className="filter-bar">
                <select 
                  value={selectedCategory} 
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </div>
          </section>

          {/* Video Grid */}
          <section className="video-grid">
            {videos.length === 0 ? (
              <div className="no-videos">
                <h3>No videos found</h3>
                <p>Try adjusting your search or filters</p>
              </div>
            ) : (
              <div className="videos-container">
                {videos.map(video => (
                  <VideoCard
                    key={video.id}
                    video={video}
                    onPlay={setCurrentVideo}
                    onApprove={handleApproveVideo}
                    onReject={handleRejectVideo}
                    isAdmin={user?.is_admin || false}
                  />
                ))}
              </div>
            )}
          </section>
        </>
      )}

      {/* Modals */}
      {currentVideo && (
        <VideoPlayer
          videoId={currentVideo}
          onClose={() => setCurrentVideo(null)}
        />
      )}

      {showUpload && (
        <UploadForm
          onClose={() => setShowUpload(false)}
          onSuccess={handleUploadSuccess}
        />
      )}
    </div>
  );
};

// Public Video Card Component (for unauthenticated users)
const PublicVideoCard = ({ video, onPlay, onShowAuth }) => {
  const thumbnailUrl = video.thumbnail 
    ? `${BACKEND_URL}/api/videos/${video.id}/thumbnail`
    : "https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?w=300&h=200&fit=crop";

  return (
    <div className="video-card">
      <div className="video-thumbnail" onClick={() => onPlay(video.id)}>
        <img 
          src={thumbnailUrl}
          alt={video.title}
        />
        <div className="play-overlay">
          <div className="play-button">▶</div>
        </div>
        <div className="video-duration">{video.duration || '00:00'}</div>
      </div>
      <div className="video-info">
        <h3 className="video-title">{video.title}</h3>
        <p className="video-description">{video.description}</p>
        <div className="video-meta">
          <span className="video-category">{video.category}</span>
          <span className="video-views">{video.views} views</span>
        </div>
        <div className="video-tags">
          {video.tags.map(tag => (
            <span key={tag} className="tag">{tag}</span>
          ))}
        </div>
      </div>
    </div>
  );
};

// Public Video Player Component (for unauthenticated users)
const PublicVideoPlayer = ({ videoId, onClose, onShowAuth }) => {
  const [showSignInPrompt, setShowSignInPrompt] = useState(false);

  useEffect(() => {
    // Show sign-in prompt after a short delay to encourage registration
    const timer = setTimeout(() => {
      setShowSignInPrompt(true);
    }, 30000); // Show after 30 seconds

    return () => clearTimeout(timer);
  }, []);

  const videoUrl = `${BACKEND_URL}/api/videos/${videoId}/stream`;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content video-modal" onClick={(e) => e.stopPropagation()}>
        <button className="modal-close" onClick={onClose}>×</button>
        <div className="video-container">
          <video 
            controls 
            autoPlay 
            className="video-player"
            src={videoUrl}
          >
            Your browser does not support the video tag.
          </video>
        </div>
        
        {showSignInPrompt && (
          <div className="sign-in-prompt">
            <div className="prompt-content">
              <h3>Enjoying the content?</h3>
              <p>Sign in to access unlimited streaming and exclusive features!</p>
              <div className="prompt-actions">
                <button onClick={onShowAuth} className="btn btn-primary">
                  Sign In Now
                </button>
                <button 
                  onClick={() => setShowSignInPrompt(false)} 
                  className="btn btn-secondary"
                >
                  Continue Watching
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Public App Component (for unauthenticated users)
const PublicApp = ({ onShowAuth }) => {
  const [videos, setVideos] = useState([]);
  const [categories, setCategories] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [currentVideo, setCurrentVideo] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchVideos();
    fetchCategories();
  }, []);

  const fetchVideos = async () => {
    try {
      // Fetch videos without authentication
      const response = await axios.get(`${API}/videos`);
      setVideos(response.data || []);
    } catch (error) {
      console.error('Failed to fetch videos:', error);
      setVideos([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get(`${API}/categories`);
      setCategories(response.data || []);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      setCategories([]);
    }
  };

  const handleSearch = async () => {
    try {
      const response = await axios.post(`${API}/search`, {
        query: searchQuery,
        category: selectedCategory
      });
      setVideos(response.data);
    } catch (error) {
      console.error('Failed to search videos:', error);
    }
  };

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  return (
    <div className="app">
      {/* Header */}
      <header className="header">
        <div className="header-content">
          <h1 className="logo">Bluefilmx</h1>
          <div className="header-actions">
            <button onClick={onShowAuth} className="btn btn-primary">
              Sign In
            </button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h2>Premium Adult Entertainment</h2>
          <p>Stream high-quality content in a safe, secure environment</p>
        </div>
        <div className="hero-image">
          <img src="https://images.unsplash.com/photo-1717295248358-4b8f2c8989d6?w=1200&h=400&fit=crop" alt="Premium streaming" />
        </div>
      </section>

      {/* Search and Filter */}
       <section className="search-section">
         <div className="search-container">
           <div className="search-bar">
             <input
               type="text"
               placeholder="Search videos..."
               value={searchQuery}
               onChange={(e) => setSearchQuery(e.target.value)}
               onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
             />
             <button onClick={handleSearch} className="btn btn-primary">Search</button>
           </div>
           <div className="filter-bar">
             <select 
               value={selectedCategory} 
               onChange={(e) => setSelectedCategory(e.target.value)}
             >
               <option value="">All Categories</option>
               {categories.map(category => (
                 <option key={category.id} value={category.name}>{category.name}</option>
               ))}
             </select>
           </div>
         </div>
       </section>

       {/* Video Grid */}
       <section className="video-grid">
         {videos.length === 0 ? (
           <div className="no-videos">
             <h3>No videos found</h3>
             <p>Try adjusting your search or filters</p>
           </div>
         ) : (
           <div className="videos-container">
             {videos.map(video => (
               <PublicVideoCard
                 key={video.id}
                 video={video}
                 onPlay={setCurrentVideo}
                 onShowAuth={onShowAuth}
               />
             ))}
           </div>
         )}
       </section>

       {/* Video Player Modal */}
       {currentVideo && (
         <PublicVideoPlayer
           videoId={currentVideo}
           onClose={() => setCurrentVideo(null)}
           onShowAuth={onShowAuth}
         />
       )}
    </div>
  );
};

// Auth Callback Component (for any remaining callback scenarios)
const AuthCallback = () => {
  const { user } = useAuth();
  
  useEffect(() => {
    // If user is already authenticated, redirect to home
    if (user) {
      window.location.href = '/';
    } else {
      // If no user and no valid callback, redirect to home
      setTimeout(() => {
        window.location.href = '/';
      }, 2000);
    }
  }, [user]);

  return (
    <div className="loading">
      <p>Redirecting...</p>
    </div>
  );
};

// Main App Router
const App = () => {
  return (
    <AuthProvider>
      <AppRouter />
    </AuthProvider>
  );
};

const AppRouter = () => {
  const { user, loading } = useAuth();
  const [showAuthModal, setShowAuthModal] = useState(false);

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  // Handle auth callback
  if (window.location.pathname === '/auth/callback') {
    return <AuthCallback />;
  }

  // If user is not logged in, show main app with limited functionality
  if (!user) {
    return (
      <>
        <PublicApp onShowAuth={() => setShowAuthModal(true)} />
        {showAuthModal && (
          <div className="modal-overlay">
            <div className="modal-content auth-modal">
              <button 
                className="modal-close" 
                onClick={() => setShowAuthModal(false)}
              >
                ×
              </button>
              <AuthForms />
            </div>
          </div>
        )}
      </>
    );
  }

  if (!user.age_verified) {
    return (
      <div className="error-container">
        <h2>Age Verification Required</h2>
        <p>You must verify your age to access this content.</p>
      </div>
    );
  }

  return <MainApp />;
};

export default App;