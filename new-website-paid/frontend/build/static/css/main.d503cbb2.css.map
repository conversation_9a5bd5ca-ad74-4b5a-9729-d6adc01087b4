{"version": 3, "file": "static/css/main.d503cbb2.css", "mappings": "AAAA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAEd,oBAAmB,CAAnB,wLAAmB,CAEnB,KACI,QAMJ,CAEA,KACI,uEAEJ,CChBA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAIE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CACzB,UAAc,CANd,mIAOF,CAEA,KAEE,kDAA6D,CAD7D,gBAEF,CAGA,QAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAA8B,CAE9B,4BAA6B,CAC7B,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CACN,WACF,CAEA,gBAME,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAH9B,aAAc,CADd,gBAAiB,CAEjB,iBAIF,CAEA,MAGE,aAAc,CAFd,cAAe,CACf,eAAiB,CAEjB,8BACF,CAQA,2BAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAGA,MAKE,kBAAmB,CADnB,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CAHvB,gBAAiB,CAIjB,eAAgB,CANhB,iBAOF,CAEA,YAKE,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAEX,UACF,CAEA,gBAEE,WAAY,CACZ,gBAAiB,CACjB,UAAY,CAHZ,UAIF,CAEA,cAGE,eAAgB,CAChB,aAAe,CAHf,iBAAkB,CAClB,SAGF,CAEA,iBACE,cAAe,CACf,mBAAqB,CACrB,6BACF,CAEA,gBACE,cAAe,CAGf,QAAS,CAFT,UAAY,CACZ,6BAEF,CAGA,gBAEE,oBAA8B,CAC9B,4BAA6B,CAF7B,YAGF,CAEA,kBAGE,YAAa,CAEb,cAAe,CADf,QAAS,CAFT,aAAc,CADd,gBAKF,CAEA,YAEE,YAAa,CADb,QAAO,CAEP,SACF,CAEA,kBAKE,oBAA8B,CAF9B,qBAAsB,CACtB,iBAAkB,CAElB,UAAW,CALX,QAAO,CAMP,cAAe,CALf,cAMF,CAEA,wBAEE,oBAAqB,CADrB,YAEF,CAEA,mBAIE,oBAA8B,CAF9B,qBAAsB,CACtB,iBAAkB,CAElB,UAAW,CACX,cAAe,CALf,cAMF,CAGA,YAGE,aAAc,CADd,gBAAiB,CADjB,YAGF,CAEA,kBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAEF,CAEA,YACE,gBAA8B,CAK9B,qBAAsB,CAJtB,iBAAkB,CAGlB,cAAe,CAFf,eAAgB,CAChB,iDAGF,CAEA,kBAEE,gCAA6C,CAD7C,0BAEF,CAEA,iBAEE,YAAa,CACb,eAAgB,CAFhB,iBAGF,CAEA,qBAEE,WAAY,CACZ,gBAAiB,CAFjB,UAGF,CAEA,cAQE,kBAAmB,CAFnB,oBAA8B,CAC9B,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CANvB,MAAO,CAOP,SAAU,CATV,iBAAkB,CAClB,KAAM,CASN,2BAA6B,CAP7B,UAQF,CAEA,gCACE,SACF,CAEA,aAME,kBAAmB,CAFnB,oBAAiC,CADjC,iBAAkB,CAMlB,UAAW,CAJX,YAAa,CAGb,gBAAiB,CANjB,WAAY,CAKZ,sBAAuB,CAGvB,8BAAwC,CATxC,UAUF,CAEA,YACE,YACF,CAEA,eAGE,UAAW,CAFX,gBAAiB,CACjB,mBAEF,CAEA,cACE,UAAW,CACX,eAAiB,CAEjB,eAAgB,CADhB,mBAEF,CAEA,YAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,mBACF,CAEA,UACE,kBAAmB,CAGnB,kBAAmB,CAFnB,UAAW,CAGX,eAAiB,CACjB,eAAiB,CAHjB,oBAIF,CAEA,OACE,UAAW,CACX,eACF,CAEA,YACE,YAAa,CAEb,cAAe,CADf,SAEF,CAEA,KACE,oBAAoC,CAGpC,iBAAkB,CAFlB,UAAW,CAGX,eAAiB,CAFjB,mBAGF,CAGA,KAEE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CAKf,oBAAqB,CAJrB,cAAe,CACf,eAAgB,CALhB,qBAAuB,CAOvB,oBAAqB,CADrB,uBAGF,CAEA,aACE,kBAAmB,CACnB,UACF,CAEA,mBACE,kBAAmB,CAEnB,+BAA4C,CAD5C,0BAEF,CAEA,eACE,eAAgB,CAChB,UACF,CAEA,qBACE,eACF,CAEA,YACE,kBAAmB,CACnB,UACF,CAEA,kBACE,kBACF,CAEA,cAEE,kBAAmB,CADnB,UAEF,CAGA,gBAGE,kBAAmB,CAEnB,kDAA6D,CAH7D,YAAa,CAEb,sBAAuB,CAHvB,gBAAiB,CAKjB,YACF,CAEA,WACE,oBAA8B,CAG9B,qBAAsB,CADtB,iBAAkB,CAIlB,gCAA0C,CAD1C,eAAgB,CAJhB,YAAa,CAGb,UAGF,CAEA,cAGE,aAAc,CADd,oBAAqB,CADrB,iBAGF,CAEA,iBAME,oBAA8B,CAF9B,qBAAsB,CACtB,iBAAkB,CAElB,UAAW,CACX,cAAe,CALf,kBAAmB,CADnB,cAAgB,CADhB,UAQF,CAEA,uBAEE,oBAAqB,CADrB,YAEF,CAEA,cAIE,UAAW,CAFX,eAAgB,CAChB,iBAAkB,CAFlB,iBAIF,CAEA,qBAOE,eAAgB,CANhB,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,OAKF,CAEA,mBACE,oBAA8B,CAC9B,cACF,CAEA,aAGE,UAAW,CADX,iBAAkB,CADlB,iBAGF,CAEA,aACE,eAAgB,CAChB,WAAY,CACZ,aAAc,CACd,cAAe,CAEf,iBAAkB,CAClB,iBAAmB,CAFnB,yBAGF,CAEA,mBACE,aACF,CAGA,eAQE,kBAAmB,CAFnB,gBAA8B,CAC9B,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CANvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAMX,YACF,CAEA,eACE,oBAA+B,CAG/B,qBAAsB,CADtB,iBAAkB,CAIlB,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CANhB,YAAa,CAIb,SAGF,CAEA,kBAEE,aAAc,CADd,kBAEF,CAEA,eACE,YAAa,CACb,QAAS,CACT,wBAAyB,CACzB,eACF,CAEA,cACE,eACF,CAEA,2CAOE,oBAA8B,CAF9B,qBAAsB,CACtB,iBAAkB,CAElB,UAAW,CACX,cAAe,CALf,kBAAmB,CADnB,cAAgB,CADhB,UAQF,CAEA,uBAEE,gBAAiB,CADjB,eAEF,CAEA,uDAGE,oBAAqB,CADrB,YAEF,CAEA,YACE,UAAW,CACX,eAAiB,CAEjB,mBAAqB,CADrB,iBAEF,CAEA,+BAGE,oBAA8B,CAD9B,sBAAuB,CAEvB,cAAe,CAHf,aAAe,CAIf,gCACF,CAEA,qCACE,oBACF,CAGA,oBAQE,kBAAmB,CAFnB,oBAA+B,CAC/B,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CANvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAMX,YACF,CAEA,sBAIE,WAAY,CADZ,gBAAiB,CAFjB,iBAAkB,CAClB,SAGF,CAEA,cAIE,eAAgB,CAChB,WAAY,CACZ,UAAW,CAEX,cAAe,CADf,cAAe,CANf,iBAAkB,CAElB,OAAQ,CADR,SAAU,CAOV,YACF,CAEA,cAGE,iBAAkB,CADlB,WAAY,CADZ,UAGF,CAGA,SAEE,kBAAmB,CAInB,UAAW,CALX,YAAa,CAIb,gBAAiB,CAFjB,sBAAuB,CACvB,eAGF,CAEA,eACE,oBAAkC,CAKlC,0BAAwC,CAFxC,iBAAkB,CAFlB,aAAc,CAGd,kBAAmB,CAFnB,cAIF,CAEA,iBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,eAAgB,CAEhB,YAAa,CADb,iBAEF,CAEA,oBACE,aAAc,CACd,kBACF,CAEA,WAGE,UAAW,CADX,iBAAkB,CADlB,iBAGF,CAEA,cACE,mBACF,CAGA,iBASE,kBAAmB,CAJnB,oBAAkC,CAClC,UAAW,CAEX,YAAa,CAIb,eAAgB,CAFhB,6BAA8B,CAP9B,MAAO,CAIP,YAAa,CANb,cAAe,CACf,KAAM,CAEN,UAAW,CAOX,YAEF,CAEA,wBACE,eAAgB,CAChB,WAAY,CAGZ,UAAW,CADX,cAAe,CADf,gBAGF,CAGA,eACE,YAAa,CACb,SAAW,CACX,gBACF,CAEA,QAGE,iBAAkB,CADlB,eAAiB,CADjB,oBAGF,CAEA,aAGE,wBAAyB,CADzB,UAEF,CAOA,YACE,kBAAmB,CAEnB,wBAAyB,CADzB,UAEF,CAEA,kBACE,kBAAmB,CACnB,oBACF,CAGA,cAKE,iBAAkB,CAClB,eAAiB,CACjB,eAAiB,CAHjB,oBAAuB,CAHvB,iBAAkB,CAElB,WAAa,CAKb,wBAAyB,CANzB,SAOF,CAEA,sBACE,oBAAkC,CAClC,UACF,CAEA,uBACE,oBAAkC,CAClC,UACF,CAGA,yBACE,gBACE,YACF,CAEA,iBACE,cACF,CAEA,kBACE,qBACF,CAEA,kBACE,yBACF,CAEA,sBAEE,WAAY,CADZ,SAEF,CAEA,WACE,WACF,CACF,CAEA,yBACE,gBACE,qBAAsB,CACtB,QACF,CAEA,iBACE,gBACF,CAEA,gBACE,cACF,CAEA,eAEE,YAAa,CADb,SAEF,CACF,CAGA,YAGE,aAAc,CADd,gBAAiB,CADjB,YAGF,CAEA,cAGE,kBAAmB,CAGnB,4BAA6B,CAL7B,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CACnB,mBAEF,CAEA,iBACE,aAAc,CACd,cACF,CAEA,sBAGE,kBAAmB,CAFnB,YAAa,CACb,QAEF,CAGA,YAGE,4BAA6B,CAF7B,YAAa,CACb,kBAEF,CAEA,YACE,eAAgB,CAMhB,WAAoC,CAApC,6BAAoC,CAHpC,UAAW,CACX,cAAe,CACf,cAAe,CAHf,iBAAkB,CAKlB,uBACF,CAEA,kBAEE,oBAAqC,CADrC,UAEF,CAEA,mBAEE,2BAA4B,CAD5B,aAEF,CAGA,qBACE,eACF,CAEA,wBACE,aAAc,CAEd,gBAAiB,CADjB,kBAEF,CAEA,aAEE,gBAA8B,CAG9B,qBAAsB,CAFtB,iBAAkB,CAClB,eAAgB,CAHhB,UAKF,CAEA,mBAEE,wBAAyB,CADzB,UAEF,CAEA,gCAIE,4BAA6B,CAF7B,YAAa,CACb,eAEF,CAEA,gBACE,oBAAiC,CACjC,aAAc,CACd,eACF,CAEA,gBACE,UACF,CAEA,sBACE,oBACF,CAEA,qBAEE,eAAiB,CACjB,kBAAoB,CAFpB,mBAGF,CAEA,aACE,kBAAmB,CACnB,oBACF,CAEA,mBACE,kBAAmB,CACnB,oBACF,CAEA,UAGE,UAAW,CADX,YAAa,CADb,iBAGF,CAGA,UAGE,gBAAiB,CAFjB,eAAgB,CAChB,iBAEF,CAGA,YAGE,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CAChB,iBAAkB,CAHlB,SAIF,CAEA,aAIE,eAAgB,CAChB,WAAY,CAGZ,UAAW,CADX,cAAe,CADf,cAAe,CALf,iBAAkB,CAElB,UAAW,CADX,QAAS,CAOT,SACF,CAEA,mBACE,UACF,CAGA,gBAKE,oBAA8B,CAG9B,iBAAkB,CANlB,WAAY,CAIZ,UAAY,CAHZ,SAAU,CAIV,YAAa,CANb,iBAAkB,CAGlB,UAAW,CAKX,iBACF,CAEA,mBAEE,aAAc,CADd,eAEF,CAEA,kBAEE,cAAe,CADf,eAEF,CAEA,gBACE,YAAa,CAGb,cAAe,CAFf,QAAS,CACT,sBAEF,CAEA,qBAEE,cAAe,CADf,gBAEF,CAGA,yBACE,UACE,cAAe,CACf,iBACF,CAEA,YAEE,WAAY,CADZ,SAEF,CAEA,gBACE,WAAY,CACZ,SAAU,CAEV,YAAa,CADb,UAEF,CAEA,gBACE,qBACF,CAEA,qBACE,UACF,CACF,CAEA,mBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAEF,CAEA,kBACE,gBAA8B,CAG9B,qBAAsB,CAFtB,iBAAkB,CAClB,eAAgB,CAEhB,iDACF,CAEA,wBAEE,+BAA4C,CAD5C,0BAEF,CAEA,mCAEE,YAAa,CACb,eAAgB,CAFhB,iBAGF,CAEA,uCAEE,WAAY,CACZ,gBAAiB,CAFjB,UAGF,CAEA,8BACE,YACF,CAEA,iCAEE,UAAW,CACX,gBAAiB,CAFjB,mBAGF,CAEA,gCACE,UAAW,CACX,eAAiB,CAEjB,eAAgB,CADhB,mBAEF,CAEA,8BAKE,UAAW,CAJX,YAAa,CAGb,eAAiB,CAFjB,6BAA8B,CAC9B,mBAGF,CAEA,8BACE,YAAa,CACb,cAAe,CACf,UAAY,CACZ,kBACF,CAEA,uBACE,oBAAiC,CAKjC,0BAAuC,CAFvC,kBAAmB,CAFnB,aAAc,CAGd,eAAiB,CAFjB,mBAIF,CAEA,iCACE,YAAa,CACb,SAAW,CACX,eACF,CAEA,sCACE,QAAO,CAEP,eAAiB,CADjB,aAEF,CAGA,kBACE,eAAgB,CAChB,SACF,CAEA,qBACE,aAAc,CACd,oBAAqB,CACrB,iBACF,CAEA,uBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,mDAKE,oBAA8B,CAF9B,qBAAsB,CACtB,iBAAkB,CAElB,UAAW,CAEX,mBAAoB,CADpB,cAAe,CALf,cAOF,CAEA,+DAGE,oBAAqB,CACrB,8BAA2C,CAF3C,YAGF,CAEA,2BAEE,gBAAiB,CADjB,eAEF,CAEA,iCACE,YAAa,CACb,QAAS,CACT,eACF,CAEA,sCACE,QAAO,CAEP,cAAe,CADf,cAEF,CAGA,yBACE,YACE,YACF,CAEA,cACE,qBAAsB,CACtB,QAAS,CACT,iBACF,CAEA,mBACE,yBACF,CAEA,kBACE,SACF,CAEA,iCACE,qBACF,CACF", "sources": ["index.css", "App.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\nbody {\n    margin: 0;\n    font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\",\n        \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\",\n        \"Helvetica Neue\", sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n    font-family: source-code-pro, Menlo, Monaco, Consolas, \"Courier New\",\n        monospace;\n}\n", "* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',\n    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #0a0a0a;\n  color: #ffffff;\n}\n\n.app {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);\n}\n\n/* Header Styles */\n.header {\n  background: rgba(0, 0, 0, 0.9);\n  backdrop-filter: blur(10px);\n  border-bottom: 1px solid #333;\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.header-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 1rem 2rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.logo {\n  font-size: 2rem;\n  font-weight: bold;\n  color: #e50914;\n  text-shadow: 0 0 10px rgba(229, 9, 20, 0.5);\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.user-menu {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n/* Hero Section */\n.hero {\n  position: relative;\n  height: 15vh;\n  min-height: 120px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n.hero-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: -1;\n}\n\n.hero-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  opacity: 0.3;\n}\n\n.hero-content {\n  text-align: center;\n  z-index: 1;\n  max-width: 600px;\n  padding: 0.5rem;\n}\n\n.hero-content h2 {\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);\n}\n\n.hero-content p {\n  font-size: 1rem;\n  opacity: 0.9;\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\n  margin: 0;\n}\n\n/* Search Section */\n.search-section {\n  padding: 2rem;\n  background: rgba(0, 0, 0, 0.3);\n  border-bottom: 1px solid #333;\n}\n\n.search-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n\n.search-bar {\n  flex: 1;\n  display: flex;\n  gap: 0.5rem;\n}\n\n.search-bar input {\n  flex: 1;\n  padding: 0.75rem;\n  border: 1px solid #555;\n  border-radius: 4px;\n  background: rgba(0, 0, 0, 0.7);\n  color: #fff;\n  font-size: 1rem;\n}\n\n.search-bar input:focus {\n  outline: none;\n  border-color: #e50914;\n}\n\n.filter-bar select {\n  padding: 0.75rem;\n  border: 1px solid #555;\n  border-radius: 4px;\n  background: rgba(0, 0, 0, 0.7);\n  color: #fff;\n  font-size: 1rem;\n}\n\n/* Video Grid */\n.video-grid {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.videos-container {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 2rem;\n}\n\n.video-card {\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 8px;\n  overflow: hidden;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  cursor: pointer;\n  border: 1px solid #333;\n}\n\n.video-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 10px 30px rgba(229, 9, 20, 0.3);\n}\n\n.video-thumbnail {\n  position: relative;\n  height: 200px;\n  overflow: hidden;\n}\n\n.video-thumbnail img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.play-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.video-card:hover .play-overlay {\n  opacity: 1;\n}\n\n.play-button {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background: rgba(229, 9, 20, 0.9);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  color: #fff;\n  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);\n}\n\n.video-info {\n  padding: 1rem;\n}\n\n.video-info h3 {\n  font-size: 1.2rem;\n  margin-bottom: 0.5rem;\n  color: #fff;\n}\n\n.video-info p {\n  color: #ccc;\n  font-size: 0.9rem;\n  margin-bottom: 0.5rem;\n  line-height: 1.4;\n}\n\n.video-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.category {\n  background: #e50914;\n  color: #fff;\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  font-weight: bold;\n}\n\n.views {\n  color: #999;\n  font-size: 0.8rem;\n}\n\n.video-tags {\n  display: flex;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.tag {\n  background: rgba(255, 255, 255, 0.1);\n  color: #ccc;\n  padding: 0.2rem 0.4rem;\n  border-radius: 8px;\n  font-size: 0.7rem;\n}\n\n/* Buttons */\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 1rem;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  text-decoration: none;\n  display: inline-block;\n}\n\n.btn-primary {\n  background: #e50914;\n  color: #fff;\n}\n\n.btn-primary:hover {\n  background: #f40612;\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(229, 9, 20, 0.4);\n}\n\n.btn-secondary {\n  background: #333;\n  color: #fff;\n}\n\n.btn-secondary:hover {\n  background: #555;\n}\n\n.btn-google {\n  background: #4285f4;\n  color: #fff;\n}\n\n.btn-google:hover {\n  background: #357ae8;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* Auth Styles */\n.auth-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);\n  padding: 2rem;\n}\n\n.auth-card {\n  background: rgba(0, 0, 0, 0.9);\n  padding: 2rem;\n  border-radius: 8px;\n  border: 1px solid #333;\n  width: 100%;\n  max-width: 400px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);\n}\n\n.auth-card h2 {\n  text-align: center;\n  margin-bottom: 1.5rem;\n  color: #e50914;\n}\n\n.auth-card input {\n  width: 100%;\n  padding: 0.75rem;\n  margin-bottom: 1rem;\n  border: 1px solid #555;\n  border-radius: 4px;\n  background: rgba(0, 0, 0, 0.7);\n  color: #fff;\n  font-size: 1rem;\n}\n\n.auth-card input:focus {\n  outline: none;\n  border-color: #e50914;\n}\n\n.auth-divider {\n  text-align: center;\n  margin: 1.5rem 0;\n  position: relative;\n  color: #666;\n}\n\n.auth-divider::before {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: #333;\n}\n\n.auth-divider span {\n  background: rgba(0, 0, 0, 0.9);\n  padding: 0 1rem;\n}\n\n.auth-switch {\n  text-align: center;\n  margin-top: 1.5rem;\n  color: #ccc;\n}\n\n.link-button {\n  background: none;\n  border: none;\n  color: #e50914;\n  cursor: pointer;\n  text-decoration: underline;\n  font-size: inherit;\n  margin-left: 0.5rem;\n}\n\n.link-button:hover {\n  color: #f40612;\n}\n\n/* Modal Styles */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background: rgba(0, 0, 0, 0.95);\n  padding: 2rem;\n  border-radius: 8px;\n  border: 1px solid #333;\n  max-width: 500px;\n  width: 90%;\n  max-height: 80vh;\n  overflow-y: auto;\n}\n\n.modal-content h2 {\n  margin-bottom: 1rem;\n  color: #e50914;\n}\n\n.modal-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n  margin-top: 1rem;\n}\n\n.upload-modal {\n  max-width: 600px;\n}\n\n.upload-modal input,\n.upload-modal textarea {\n  width: 100%;\n  padding: 0.75rem;\n  margin-bottom: 1rem;\n  border: 1px solid #555;\n  border-radius: 4px;\n  background: rgba(0, 0, 0, 0.7);\n  color: #fff;\n  font-size: 1rem;\n}\n\n.upload-modal textarea {\n  resize: vertical;\n  min-height: 100px;\n}\n\n.upload-modal input:focus,\n.upload-modal textarea:focus {\n  outline: none;\n  border-color: #e50914;\n}\n\n.file-label {\n  color: #ccc;\n  font-size: 0.9rem;\n  margin-top: -0.5rem;\n  margin-bottom: 0.5rem;\n}\n\n.upload-modal input[type=\"file\"] {\n  padding: 0.5rem;\n  border: 2px dashed #555;\n  background: rgba(0, 0, 0, 0.5);\n  cursor: pointer;\n  transition: border-color 0.3s ease;\n}\n\n.upload-modal input[type=\"file\"]:hover {\n  border-color: #e50914;\n}\n\n/* Video Player */\n.video-player-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.95);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.video-player-content {\n  position: relative;\n  width: 90%;\n  max-width: 1000px;\n  height: 70vh;\n}\n\n.close-button {\n  position: absolute;\n  top: -40px;\n  right: 0;\n  background: none;\n  border: none;\n  color: #fff;\n  font-size: 2rem;\n  cursor: pointer;\n  z-index: 1001;\n}\n\n.video-player {\n  width: 100%;\n  height: 100%;\n  border-radius: 8px;\n}\n\n/* Loading and Error States */\n.loading {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 50vh;\n  font-size: 1.2rem;\n  color: #ccc;\n}\n\n.error-message {\n  background: rgba(220, 53, 69, 0.2);\n  color: #ff6b6b;\n  padding: 0.75rem;\n  border-radius: 4px;\n  margin-bottom: 1rem;\n  border: 1px solid rgba(220, 53, 69, 0.3);\n}\n\n.error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 50vh;\n  text-align: center;\n  padding: 2rem;\n}\n\n.error-container h2 {\n  color: #e50914;\n  margin-bottom: 1rem;\n}\n\n.no-videos {\n  text-align: center;\n  padding: 4rem 2rem;\n  color: #ccc;\n}\n\n.no-videos h3 {\n  margin-bottom: 0.5rem;\n}\n\n/* Approval Banner */\n.approval-banner {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  background: rgba(255, 193, 7, 0.9);\n  color: #000;\n  padding: 1rem;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  z-index: 1000;\n  font-weight: 500;\n}\n\n.approval-banner button {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #000;\n}\n\n/* Admin Actions */\n.admin-actions {\n  display: flex;\n  gap: 0.5rem;\n  margin-top: 0.5rem;\n}\n\n.btn-sm {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.8rem;\n  border-radius: 4px;\n}\n\n.btn-success {\n  background: #28a745;\n  color: white;\n  border: 1px solid #28a745;\n}\n\n.btn-success:hover {\n  background: #218838;\n  border-color: #1e7e34;\n}\n\n.btn-danger {\n  background: #dc3545;\n  color: white;\n  border: 1px solid #dc3545;\n}\n\n.btn-danger:hover {\n  background: #c82333;\n  border-color: #bd2130;\n}\n\n/* Video Status */\n.video-status {\n  position: absolute;\n  top: 0.5rem;\n  right: 0.5rem;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n  font-size: 0.7rem;\n  font-weight: bold;\n  text-transform: uppercase;\n}\n\n.video-status.pending {\n  background: rgba(255, 193, 7, 0.9);\n  color: #000;\n}\n\n.video-status.rejected {\n  background: rgba(220, 53, 69, 0.9);\n  color: #fff;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .header-content {\n    padding: 1rem;\n  }\n  \n  .hero-content h2 {\n    font-size: 2rem;\n  }\n  \n  .search-container {\n    flex-direction: column;\n  }\n  \n  .videos-container {\n    grid-template-columns: 1fr;\n  }\n  \n  .video-player-content {\n    width: 95%;\n    height: 60vh;\n  }\n  \n  .auth-card {\n    margin: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .header-content {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  \n  .hero-content h2 {\n    font-size: 1.5rem;\n  }\n  \n  .hero-content p {\n    font-size: 1rem;\n  }\n  \n  .modal-content {\n    width: 95%;\n    padding: 1rem;\n  }\n}\n\n/* Admin Page Styles */\n.admin-page {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.admin-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid #333;\n}\n\n.admin-header h2 {\n  color: #e50914;\n  font-size: 2rem;\n}\n\n.admin-header-actions {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n}\n\n/* Admin Tabs */\n.admin-tabs {\n  display: flex;\n  margin-bottom: 2rem;\n  border-bottom: 1px solid #333;\n}\n\n.tab-button {\n  background: none;\n  border: none;\n  padding: 1rem 2rem;\n  color: #ccc;\n  cursor: pointer;\n  font-size: 1rem;\n  border-bottom: 2px solid transparent;\n  transition: all 0.3s ease;\n}\n\n.tab-button:hover {\n  color: #fff;\n  background: rgba(255, 255, 255, 0.05);\n}\n\n.tab-button.active {\n  color: #e50914;\n  border-bottom-color: #e50914;\n}\n\n/* User Management Styles */\n.admin-users-section {\n  margin-top: 1rem;\n}\n\n.admin-users-section h3 {\n  color: #e50914;\n  margin-bottom: 1rem;\n  font-size: 1.5rem;\n}\n\n.users-table {\n  width: 100%;\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333;\n}\n\n.users-table table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.users-table th,\n.users-table td {\n  padding: 1rem;\n  text-align: left;\n  border-bottom: 1px solid #333;\n}\n\n.users-table th {\n  background: rgba(229, 9, 20, 0.1);\n  color: #e50914;\n  font-weight: 600;\n}\n\n.users-table td {\n  color: #fff;\n}\n\n.users-table tr:hover {\n  background: rgba(255, 255, 255, 0.05);\n}\n\n.users-table .btn-sm {\n  padding: 0.4rem 0.8rem;\n  font-size: 0.8rem;\n  margin-right: 0.5rem;\n}\n\n.btn-success {\n  background: #28a745;\n  border-color: #28a745;\n}\n\n.btn-success:hover {\n  background: #218838;\n  border-color: #1e7e34;\n}\n\n.no-users {\n  text-align: center;\n  padding: 2rem;\n  color: #ccc;\n}\n\n/* Public App Styles */\n.hero-cta {\n  margin-top: 1rem;\n  padding: 12px 24px;\n  font-size: 1.1rem;\n}\n\n/* Auth Modal Styles */\n.auth-modal {\n  max-width: 500px;\n  width: 90%;\n  max-height: 90vh;\n  overflow-y: auto;\n  position: relative;\n}\n\n.modal-close {\n  position: absolute;\n  top: 15px;\n  right: 20px;\n  background: none;\n  border: none;\n  font-size: 24px;\n  cursor: pointer;\n  color: #666;\n  z-index: 1;\n}\n\n.modal-close:hover {\n  color: #000;\n}\n\n/* Sign-in Prompt Styles */\n.sign-in-prompt {\n  position: absolute;\n  bottom: 20px;\n  left: 20px;\n  right: 20px;\n  background: rgba(0, 0, 0, 0.9);\n  color: white;\n  padding: 20px;\n  border-radius: 8px;\n  text-align: center;\n}\n\n.prompt-content h3 {\n  margin: 0 0 10px 0;\n  color: #ff6b6b;\n}\n\n.prompt-content p {\n  margin: 0 0 15px 0;\n  font-size: 14px;\n}\n\n.prompt-actions {\n  display: flex;\n  gap: 10px;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n.prompt-actions .btn {\n  padding: 8px 16px;\n  font-size: 14px;\n}\n\n/* Responsive adjustments for public components */\n@media (max-width: 768px) {\n  .hero-cta {\n    font-size: 1rem;\n    padding: 10px 20px;\n  }\n  \n  .auth-modal {\n    width: 95%;\n    margin: 20px;\n  }\n  \n  .sign-in-prompt {\n    bottom: 10px;\n    left: 10px;\n    right: 10px;\n    padding: 15px;\n  }\n  \n  .prompt-actions {\n    flex-direction: column;\n  }\n  \n  .prompt-actions .btn {\n    width: 100%;\n  }\n}\n\n.admin-videos-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 2rem;\n}\n\n.admin-video-card {\n  background: rgba(0, 0, 0, 0.8);\n  border-radius: 8px;\n  overflow: hidden;\n  border: 1px solid #333;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n}\n\n.admin-video-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 20px rgba(229, 9, 20, 0.2);\n}\n\n.admin-video-card .video-thumbnail {\n  position: relative;\n  height: 200px;\n  overflow: hidden;\n}\n\n.admin-video-card .video-thumbnail img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.admin-video-card .video-info {\n  padding: 1rem;\n}\n\n.admin-video-card .video-info h3 {\n  margin-bottom: 0.5rem;\n  color: #fff;\n  font-size: 1.1rem;\n}\n\n.admin-video-card .video-info p {\n  color: #ccc;\n  font-size: 0.9rem;\n  margin-bottom: 0.5rem;\n  line-height: 1.4;\n}\n\n.admin-video-card .video-meta {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.5rem;\n  font-size: 0.8rem;\n  color: #999;\n}\n\n.admin-video-card .video-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.25rem;\n  margin-bottom: 1rem;\n}\n\n.admin-video-card .tag {\n  background: rgba(229, 9, 20, 0.2);\n  color: #e50914;\n  padding: 0.2rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  border: 1px solid rgba(229, 9, 20, 0.3);\n}\n\n.admin-video-card .admin-actions {\n  display: flex;\n  gap: 0.5rem;\n  margin-top: 1rem;\n}\n\n.admin-video-card .admin-actions .btn {\n  flex: 1;\n  padding: 0.5rem;\n  font-size: 0.9rem;\n}\n\n/* Edit Video Modal */\n.edit-video-modal {\n  max-width: 500px;\n  width: 90%;\n}\n\n.edit-video-modal h2 {\n  color: #e50914;\n  margin-bottom: 1.5rem;\n  text-align: center;\n}\n\n.edit-video-modal form {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.edit-video-modal input,\n.edit-video-modal textarea {\n  padding: 0.75rem;\n  border: 1px solid #555;\n  border-radius: 4px;\n  background: rgba(0, 0, 0, 0.7);\n  color: #fff;\n  font-size: 1rem;\n  font-family: inherit;\n}\n\n.edit-video-modal input:focus,\n.edit-video-modal textarea:focus {\n  outline: none;\n  border-color: #e50914;\n  box-shadow: 0 0 0 2px rgba(229, 9, 20, 0.2);\n}\n\n.edit-video-modal textarea {\n  resize: vertical;\n  min-height: 100px;\n}\n\n.edit-video-modal .modal-actions {\n  display: flex;\n  gap: 1rem;\n  margin-top: 1rem;\n}\n\n.edit-video-modal .modal-actions .btn {\n  flex: 1;\n  padding: 0.75rem;\n  font-size: 1rem;\n}\n\n/* Admin responsive design */\n@media (max-width: 768px) {\n  .admin-page {\n    padding: 1rem;\n  }\n  \n  .admin-header {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n  \n  .admin-videos-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .edit-video-modal {\n    width: 95%;\n  }\n  \n  .edit-video-modal .modal-actions {\n    flex-direction: column;\n  }\n}"], "names": [], "sourceRoot": ""}