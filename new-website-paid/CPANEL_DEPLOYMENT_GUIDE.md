# cPanel Deployment Guide - PHP Backend

This guide provides step-by-step instructions for deploying your full-stack application to cPanel using PHP backend instead of Python FastAPI.

## Why PHP Backend?

Your cPanel hosting supports:
- ✅ PHP with Apache
- ✅ MariaDB Database
- ✅ File Manager for easy uploads
- ✅ Simple configuration

This eliminates the need for:
- ❌ Python environment setup
- ❌ Package compatibility issues
- ❌ SSH complexities
- ❌ Process management (PM2)

## Server Information
- **Server**: corporate
- **Apache**: 2.4.63
- **Database**: MariaDB 10.6.23
- **PHP**: Available via apache_php_fpm

## Deployment Steps

### 1. Database Setup

1. **Create Database in cPanel:**
   - Go to cPanel → MySQL Databases
   - Create a new database: `your_username_appdb`
   - Create a database user: `your_username_appuser`
   - Add user to database with ALL PRIVILEGES
   - Note down: database name, username, password

### 2. Upload Files

1. **Compress your project:**
   ```bash
   cd /Users/<USER>/Desktop/newpaid
   tar -czf website-deployment.tar.gz new-website-paid/
   ```

2. **Upload via cPanel File Manager:**
   - Go to cPanel → File Manager
   - Navigate to your home directory
   - Upload `website-deployment.tar.gz`
   - Extract the archive

### 3. Configure Backend

1. **Update database configuration:**
   - Edit `backend-php/config/config.php`
   - Update database credentials:
   ```php
   define('DB_NAME', 'your_username_appdb');
   define('DB_USER', 'your_username_appuser');
   define('DB_PASS', 'your_database_password');
   ```

2. **Update domain settings:**
   ```php
   define('SITE_URL', 'https://yourdomain.com');
   define('API_URL', 'https://yourdomain.com/api');
   define('GOOGLE_REDIRECT_URI', 'https://yourdomain.com/api/auth/google/callback');
   ```

3. **Generate secure JWT secret:**
   ```php
   define('JWT_SECRET', 'your-32-character-random-string-here');
   ```

### 4. Setup File Structure

1. **Move backend to subdomain/directory:**
   - Copy `backend-php/` contents to `public_html/api/`
   - Ensure proper permissions (755 for directories, 644 for files)

2. **Create uploads directory:**
   - Create `public_html/uploads/` directory
   - Set permissions to 755

### 5. Frontend Configuration

1. **Update frontend environment:**
   - Edit `frontend/.env`
   - Set: `REACT_APP_BACKEND_URL=https://yourdomain.com`

2. **Build frontend:**
   ```bash
   cd frontend
   npm install
   npm run build
   ```

3. **Upload frontend:**
   - Copy contents of `build/` folder to `public_html/`
   - Ensure `index.html` is in the root of `public_html/`

### 6. Configure Web Server

1. **Create main .htaccess in public_html:**
   ```apache
   RewriteEngine On
   
   # API routes
   RewriteRule ^api/(.*)$ api/index.php [QSA,L]
   
   # Handle React Router
   RewriteCond %{REQUEST_FILENAME} !-f
   RewriteCond %{REQUEST_FILENAME} !-d
   RewriteRule . /index.html [L]
   
   # Security headers
   Header always set X-Content-Type-Options nosniff
   Header always set X-Frame-Options DENY
   Header always set X-XSS-Protection "1; mode=block"
   ```

### 7. Test Deployment

1. **Test API endpoints:**
   - `https://yourdomain.com/api/auth/register`
   - `https://yourdomain.com/api/auth/login`
   - `https://yourdomain.com/api/upload`

2. **Test frontend:**
   - Visit `https://yourdomain.com`
   - Test user registration/login
   - Test file upload functionality

## File Structure After Deployment

```
public_html/
├── index.html (React app)
├── static/ (React assets)
├── .htaccess (Main routing)
├── api/
│   ├── index.php (Main API router)
│   ├── .htaccess (API routing)
│   ├── config/
│   │   ├── config.php
│   │   ├── database.php
│   │   └── jwt.php
│   ├── models/
│   │   └── User.php
│   └── controllers/
│       ├── AuthController.php
│       └── UploadController.php
└── uploads/
    ├── .htaccess (Security)
    └── [uploaded files]
```

## Security Considerations

1. **Database Security:**
   - Use strong database passwords
   - Limit database user privileges
   - Regular backups

2. **File Upload Security:**
   - File type validation
   - Size limits enforced
   - No PHP execution in uploads

3. **API Security:**
   - JWT token authentication
   - CORS properly configured
   - Input validation and sanitization

## Environment Variables to Update

1. **Database:**
   - `DB_NAME`: Your cPanel database name
   - `DB_USER`: Your cPanel database user
   - `DB_PASS`: Your cPanel database password

2. **Domain:**
   - `SITE_URL`: Your actual domain
   - `API_URL`: Your API endpoint URL

3. **Authentication:**
   - `JWT_SECRET`: Generate a secure 32+ character string
   - `GOOGLE_CLIENT_ID`: Your Google OAuth client ID
   - `GOOGLE_CLIENT_SECRET`: Your Google OAuth secret

4. **Stripe (if using payments):**
   - `STRIPE_SECRET_KEY`: Your live Stripe secret key
   - `STRIPE_PUBLISHABLE_KEY`: Your live Stripe publishable key

## Troubleshooting

1. **Database Connection Issues:**
   - Verify database credentials
   - Check database user permissions
   - Ensure database exists

2. **File Upload Issues:**
   - Check directory permissions
   - Verify PHP upload limits
   - Check file type restrictions

3. **API Not Working:**
   - Check .htaccess configuration
   - Verify PHP error logs
   - Test API endpoints directly

4. **Frontend Issues:**
   - Verify build process completed
   - Check React Router configuration
   - Ensure API URL is correct

## Advantages of This Approach

✅ **Simple Upload**: Just use cPanel File Manager
✅ **No SSH Required**: Everything through cPanel interface
✅ **Native Support**: PHP and MariaDB are fully supported
✅ **Easy Maintenance**: Standard cPanel tools for management
✅ **Better Performance**: Optimized for shared hosting environment
✅ **Cost Effective**: Uses existing hosting features

This approach leverages your cPanel hosting capabilities effectively and provides a much simpler deployment process compared to the Python FastAPI approach.