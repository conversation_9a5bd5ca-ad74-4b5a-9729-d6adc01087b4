<?php
// Configuration file for cPanel deployment

// Database Configuration
// For local testing with SQLite
//define('DB_HOST', 'sqlite');
//define('DB_NAME', '../database/local_test.db'); // SQLite database file
//define('DB_USER', '');
//define('DB_PASS', '');

// For cPanel deployment, uncomment and update these:
define('DB_HOST', 'localhost');
define('DB_NAME', 'bluefilm_cheapghan_bluefilmx_db'); // e.g., username_dbname
define('DB_USER', 'bluefilm_cheapghan_bluefilmx_user');   // e.g., username_dbuser
define('DB_PASS', 'kingpatrick100');

// JWT Configuration
define('JWT_SECRET', 'vK9!eT7#qM2^zP4@hX6&nB8*dJ0$wL5%');

// Google OAuth Configuration
define('GOOGLE_CLIENT_ID', '192125123148-dveqlhnvnho74moe74r0b9sg2u9dssoa.apps.googleusercontent.com');
define('GOOGLE_CLIENT_SECRET', '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');
define('GOOGLE_REDIRECT_URI', 'https://bluefilmx.com/api/auth/google/callback');

// Stripe Configuration
define('STRIPE_SECRET_KEY', 'sk_live_your_live_stripe_key_here');
define('STRIPE_PUBLISHABLE_KEY', 'pk_live_your_live_publishable_key_here');

// File Upload Configuration
define('UPLOAD_MAX_SIZE', 100 * 1024 * 1024); // 100MB
define('UPLOAD_DIR', '../uploads/');

// Allowed file types
define('ALLOWED_MIME_TYPES', [
    'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv',
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
    'audio/mp3', 'audio/wav', 'audio/ogg'
]);

// Site Configuration
define('SITE_URL', 'https://bluefilmx.com');
define('API_URL', 'https://bluefilmx.com/api');

// Error Reporting (set to false in production)
define('DEBUG_MODE', false);

if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}
?>