<?php
class UploadController {
    private $db;
    private $upload_dir;
    
    public function __construct($db) {
        $this->db = $db;
        $this->upload_dir = '../uploads/';
        
        // Create uploads directory if it doesn't exist
        if (!file_exists($this->upload_dir)) {
            mkdir($this->upload_dir, 0755, true);
        }
        
        // Create uploads table if not exists
        $this->createUploadsTable();
    }
    
    private function createUploadsTable() {
        $query = "CREATE TABLE IF NOT EXISTS uploads (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            filename VARCHAR(255) NOT NULL,
            original_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INT NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute();
    }
    
    public function uploadFile() {
        // Verify JWT token
        $headers = getallheaders();
        $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
        
        if (!$token) {
            http_response_code(401);
            echo json_encode(['error' => 'No token provided']);
            return;
        }
        
        $decoded = JWT::verifyToken($token);
        if (!$decoded) {
            http_response_code(401);
            echo json_encode(['error' => 'Invalid token']);
            return;
        }
        
        if (!isset($_FILES['file'])) {
            http_response_code(400);
            echo json_encode(['error' => 'No file uploaded']);
            return;
        }
        
        $file = $_FILES['file'];
        
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            http_response_code(400);
            echo json_encode(['error' => 'File upload error: ' . $file['error']]);
            return;
        }
        
        // Validate file size (max 100MB)
        $max_size = 100 * 1024 * 1024; // 100MB
        if ($file['size'] > $max_size) {
            http_response_code(400);
            echo json_encode(['error' => 'File too large. Maximum size is 100MB']);
            return;
        }
        
        // Validate file type
        $allowed_types = [
            'video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv',
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
            'audio/mp3', 'audio/wav', 'audio/ogg'
        ];
        
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!in_array($mime_type, $allowed_types)) {
            http_response_code(400);
            echo json_encode(['error' => 'File type not allowed']);
            return;
        }
        
        // Generate unique filename
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $unique_filename = uniqid() . '.' . $file_extension;
        $file_path = $this->upload_dir . $unique_filename;
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $file_path)) {
            // Save file info to database
            $query = "INSERT INTO uploads (user_id, filename, original_name, file_path, file_size, mime_type) 
                      VALUES (:user_id, :filename, :original_name, :file_path, :file_size, :mime_type)";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':user_id', $decoded['user_id']);
            $stmt->bindParam(':filename', $unique_filename);
            $stmt->bindParam(':original_name', $file['name']);
            $stmt->bindParam(':file_path', $file_path);
            $stmt->bindParam(':file_size', $file['size']);
            $stmt->bindParam(':mime_type', $mime_type);
            
            if ($stmt->execute()) {
                $upload_id = $this->db->lastInsertId();
                
                echo json_encode([
                    'message' => 'File uploaded successfully',
                    'upload_id' => $upload_id,
                    'filename' => $unique_filename,
                    'original_name' => $file['name'],
                    'file_size' => $file['size'],
                    'mime_type' => $mime_type,
                    'download_url' => '/uploads/' . $unique_filename
                ]);
            } else {
                // Delete uploaded file if database insert fails
                unlink($file_path);
                http_response_code(500);
                echo json_encode(['error' => 'Failed to save file info']);
            }
        } else {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to move uploaded file']);
        }
    }
    
    public function getUserUploads($user_id) {
        $query = "SELECT id, filename, original_name, file_size, mime_type, upload_date 
                  FROM uploads WHERE user_id = :user_id ORDER BY upload_date DESC";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        
        $uploads = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Add download URLs
        foreach ($uploads as &$upload) {
            $upload['download_url'] = '/uploads/' . $upload['filename'];
        }
        
        return $uploads;
    }
    
    public function getVideos() {
        // Get all public videos (you can add pagination later)
        $query = "SELECT * FROM uploads ORDER BY upload_date DESC LIMIT 50";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode($videos);
    }
    
    public function getCategories() {
        // Return some default categories for now
        $categories = [
            ['id' => 1, 'name' => 'Entertainment'],
            ['id' => 2, 'name' => 'Education'],
            ['id' => 3, 'name' => 'Music'],
            ['id' => 4, 'name' => 'Sports'],
            ['id' => 5, 'name' => 'Technology'],
            ['id' => 6, 'name' => 'Gaming']
        ];
        
        echo json_encode($categories);
    }
}
?>