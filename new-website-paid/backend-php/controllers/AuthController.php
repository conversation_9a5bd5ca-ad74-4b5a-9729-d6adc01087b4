<?php
require_once '../config/jwt.php';

class AuthController {
    private $db;
    private $user;
    
    public function __construct($db) {
        $this->db = $db;
        $this->user = new User($db);
        // Create user table if not exists
        $this->user->createTable();
    }
    
    public function register() {
        $data = json_decode(file_get_contents("php://input"));
        
        if (!isset($data->email) || !isset($data->password) || !isset($data->name)) {
            http_response_code(400);
            echo json_encode(['error' => 'Missing required fields']);
            return;
        }
        
        // Check if user already exists
        if ($this->user->findByEmail($data->email)) {
            http_response_code(409);
            echo json_encode(['error' => 'User already exists']);
            return;
        }
        
        // Create new user
        $this->user->email = $data->email;
        $this->user->password = $data->password;
        $this->user->name = $data->name;
        $this->user->google_id = null;
        
        if ($this->user->create()) {
            $token = JWT::generateToken($this->user->id, $this->user->email);
            
            http_response_code(201);
            echo json_encode([
                'message' => 'User created successfully',
                'token' => $token,
                'user' => $this->user->toArray()
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to create user']);
        }
    }
    
    public function login() {
        $data = json_decode(file_get_contents("php://input"));
        
        if (!isset($data->email) || !isset($data->password)) {
            http_response_code(400);
            echo json_encode(['error' => 'Email and password required']);
            return;
        }
        
        if ($this->user->findByEmail($data->email)) {
            if ($this->user->verifyPassword($data->password)) {
                $token = JWT::generateToken($this->user->id, $this->user->email);
                
                echo json_encode([
                    'message' => 'Login successful',
                    'token' => $token,
                    'user' => $this->user->toArray()
                ]);
            } else {
                http_response_code(401);
                echo json_encode(['error' => 'Invalid credentials']);
            }
        } else {
            http_response_code(401);
            echo json_encode(['error' => 'User not found']);
        }
    }
    
    public function googleAuth() {
        $data = json_decode(file_get_contents("php://input"));
        
        if (!isset($data->google_token)) {
            http_response_code(400);
            echo json_encode(['error' => 'Google token required']);
            return;
        }
        
        // Verify Google token (simplified - in production, verify with Google API)
        $google_user = $this->verifyGoogleToken($data->google_token);
        
        if (!$google_user) {
            http_response_code(401);
            echo json_encode(['error' => 'Invalid Google token']);
            return;
        }
        
        // Check if user exists by Google ID
        if ($this->user->findByGoogleId($google_user['id'])) {
            // User exists, login
            $token = JWT::generateToken($this->user->id, $this->user->email);
            echo json_encode([
                'message' => 'Google login successful',
                'token' => $token,
                'user' => $this->user->toArray()
            ]);
        } else {
            // Check if user exists by email
            if ($this->user->findByEmail($google_user['email'])) {
                // Update existing user with Google ID
                $this->user->google_id = $google_user['id'];
                // Update user in database (simplified)
            } else {
                // Create new user
                $this->user->email = $google_user['email'];
                $this->user->name = $google_user['name'];
                $this->user->google_id = $google_user['id'];
                $this->user->password = null;
                
                if (!$this->user->create()) {
                    http_response_code(500);
                    echo json_encode(['error' => 'Failed to create user']);
                    return;
                }
            }
            
            $token = JWT::generateToken($this->user->id, $this->user->email);
            echo json_encode([
                'message' => 'Google registration successful',
                'token' => $token,
                'user' => $this->user->toArray()
            ]);
        }
    }
    
    public function getProfile() {
        $headers = getallheaders();
        $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
        
        if (!$token) {
            http_response_code(401);
            echo json_encode(['error' => 'No token provided']);
            return;
        }
        
        $decoded = JWT::verifyToken($token);
        if (!$decoded) {
            http_response_code(401);
            echo json_encode(['error' => 'Invalid token']);
            return;
        }
        
        if ($this->user->findByEmail($decoded['email'])) {
            echo json_encode([
                'user' => $this->user->toArray()
            ]);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'User not found']);
        }
    }
    
    public function googleLogin() {
        // Redirect to Google OAuth
        $client_id = 'your-google-client-id';
        $redirect_uri = 'http://localhost:8080/api/auth/google/callback';
        $scope = 'openid email profile';
        
        $google_url = 'https://accounts.google.com/o/oauth2/v2/auth?' . http_build_query([
            'client_id' => $client_id,
            'redirect_uri' => $redirect_uri,
            'scope' => $scope,
            'response_type' => 'code',
            'access_type' => 'offline'
        ]);
        
        header('Location: ' . $google_url);
        exit;
    }
    
    public function googleCallback() {
        $code = $_GET['code'] ?? null;
        
        if (!$code) {
            http_response_code(400);
            echo json_encode(['error' => 'Authorization code not provided']);
            return;
        }
        
        // For now, return a simple success response
        // In production, exchange code for token and get user info
        $token = JWT::generateToken('google_user_123', '<EMAIL>');
        
        // Return JavaScript to close popup and send data to parent
        header('Content-Type: text/html');
        echo '<script>
            window.opener.postMessage({
                type: "GOOGLE_AUTH_SUCCESS",
                token: "' . $token . '",
                user: {
                    id: "google_user_123",
                    email: "<EMAIL>",
                    name: "Google User"
                }
            }, "*");
            window.close();
        </script>';
    }
    
    private function verifyGoogleToken($token) {
        // Simplified Google token verification
        // In production, use Google's API to verify the token
        // For now, return mock data
        return [
            'id' => 'google_user_id',
            'email' => '<EMAIL>',
            'name' => 'Google User'
        ];
    }
}
?>