<?php
// Router for PHP built-in server
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Handle API requests
if (strpos($uri, '/api/') === 0) {
    // Set the original URI for the API handler
    $_SERVER['ORIGINAL_REQUEST_URI'] = $_SERVER['REQUEST_URI'];
    require_once __DIR__ . '/api/index.php';
    return true;
}

// For other files, let the server handle them normally
return false;
?>