<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../controllers/AuthController.php';
require_once __DIR__ . '/../controllers/UploadController.php';

// Get the request URI and method
$request_uri = $_SERVER['REQUEST_URI'];
$request_method = $_SERVER['REQUEST_METHOD'];

// Remove query string and get path
$path = parse_url($request_uri, PHP_URL_PATH);
$path = str_replace('/api', '', $path);

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Route the requests
switch ($path) {
    case '/auth/register':
        if ($request_method === 'POST') {
            $authController = new AuthController($db);
            $authController->register();
        }
        break;
        
    case '/auth/login':
        if ($request_method === 'POST') {
            $authController = new AuthController($db);
            $authController->login();
        }
        break;
        
    case '/auth/google':
        if ($request_method === 'POST') {
            $authController = new AuthController($db);
            $authController->googleAuth();
        }
        break;
        
    case '/auth/google/login':
        if ($request_method === 'GET') {
            $authController = new AuthController($db);
            $authController->googleLogin();
        }
        break;
        
    case '/auth/google/callback':
        if ($request_method === 'GET') {
            $authController = new AuthController($db);
            $authController->googleCallback();
        }
        break;
        
    case '/upload':
        if ($request_method === 'POST') {
            $uploadController = new UploadController($db);
            $uploadController->uploadFile();
        }
        break;
        
    case '/user/profile':
        if ($request_method === 'GET') {
            $authController = new AuthController($db);
            $authController->getProfile();
        }
        break;
        
    case '/videos':
        if ($request_method === 'GET') {
            $uploadController = new UploadController($db);
            $uploadController->getVideos();
        }
        break;
        
    case '/categories':
        if ($request_method === 'GET') {
            echo json_encode([
                ['id' => 1, 'name' => 'Entertainment'],
                ['id' => 2, 'name' => 'Education'],
                ['id' => 3, 'name' => 'Music'],
                ['id' => 4, 'name' => 'Sports'],
                ['id' => 5, 'name' => 'Technology'],
                ['id' => 6, 'name' => 'Gaming']
            ]);
        }
        break;
        
    case '/test':
        echo json_encode(['message' => 'API is working!', 'path' => $path, 'method' => $request_method]);
        break;
        
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Endpoint not found', 'path' => $path]);
        break;
}
?>