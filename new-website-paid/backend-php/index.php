<?php
// Simple routing for PHP built-in server
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/models/User.php';
require_once __DIR__ . '/controllers/AuthController.php';
require_once __DIR__ . '/controllers/UploadController.php';

$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$method = $_SERVER['REQUEST_METHOD'];

// Initialize database connection
$database = new Database();
$db = $database->getConnection();

// Authentication endpoints
if ($uri === '/api/auth/register' && $method === 'POST') {
    $authController = new AuthController($db);
    $authController->register();
} elseif ($uri === '/api/auth/login' && $method === 'POST') {
    $authController = new AuthController($db);
    $authController->login();
} elseif ($uri === '/api/auth/google' && $method === 'POST') {
    $authController = new AuthController($db);
    $authController->googleAuth();
} elseif ($uri === '/api/auth/google/login' && $method === 'GET') {
    // Google OAuth login endpoint
    $authController = new AuthController($db);
    $authController->googleLogin();
} elseif ($uri === '/api/auth/google/callback' && $method === 'GET') {
    // Google OAuth callback endpoint
    $authController = new AuthController($db);
    $authController->googleCallback();
} elseif ($uri === '/api/auth/profile' && $method === 'GET') {
    // Get user profile endpoint
    $authController = new AuthController($db);
    $authController->getProfile();
} elseif ($uri === '/api/categories' && $method === 'GET') {
    echo json_encode([
        ['id' => 1, 'name' => 'Entertainment'],
        ['id' => 2, 'name' => 'Education'],
        ['id' => 3, 'name' => 'Music'],
        ['id' => 4, 'name' => 'Sports'],
        ['id' => 5, 'name' => 'Technology'],
        ['id' => 6, 'name' => 'Gaming']
    ]);
} elseif ($uri === '/api/videos' && $method === 'GET') {
    echo json_encode([]);
} elseif ($uri === '/api/test' && $method === 'GET') {
    echo json_encode(['message' => 'API is working!', 'uri' => $uri, 'method' => $method]);
} else {
    http_response_code(404);
    echo json_encode(['error' => 'Endpoint not found', 'uri' => $uri, 'method' => $method]);
}
?>