# Allow access to uploaded files
Options -Indexes

# Security headers for file downloads
Head<PERSON> always set X-Content-Type-Options nosniff
Header always set X-Frame-Options SAMEORIGIN

# Prevent execution of PHP files in uploads directory
<Files "*.php">
    Require all denied
</Files>

<Files "*.phtml">
    Require all denied
</Files>

<Files "*.php3">
    Require all denied
</Files>

<Files "*.php4">
    Require all denied
</Files>

<Files "*.php5">
    Require all denied
</Files>

# Allow specific file types only
<FilesMatch "\.(jpg|jpeg|png|gif|webp|mp4|avi|mov|wmv|flv|mp3|wav|ogg)$">
    Require all granted
</FilesMatch>

# Deny all other file types
<FilesMatch "^(?!.*\.(jpg|jpeg|png|gif|webp|mp4|avi|mov|wmv|flv|mp3|wav|ogg)$).*$">
    Require all denied
</FilesMatch>