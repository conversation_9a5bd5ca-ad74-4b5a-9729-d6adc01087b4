from fastapi import <PERSON><PERSON><PERSON>, API<PERSON><PERSON>er, HTTPException, Depends, File, UploadFile, Form, Request, BackgroundTasks
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import StreamingResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import os
import logging
from pathlib import Path
from pydantic import BaseModel, Field
from typing import List, Optional
import uuid
from datetime import datetime, timedelta
import hashlib
import jwt
import aiofiles
import mimetypes
from authlib.integrations.starlette_client import OAuth
import httpx
from google.auth.transport import requests as google_requests
from google.oauth2 import id_token
import shutil

ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# MongoDB connection
mongo_url = os.environ['MONGO_URL']
client = AsyncIOMotorClient(mongo_url)
db = client[os.environ['DB_NAME']]

# JWT Secret
JWT_SECRET = os.environ.get('JWT_SECRET', 'your-secret-key-here')

# Create uploads directory
uploads_dir = Path("../uploads")
uploads_dir.mkdir(exist_ok=True)

# Create the main app
app = FastAPI(title="Adult Content Platform API")

# Add session middleware
app.add_middleware(SessionMiddleware, secret_key=JWT_SECRET)

# OAuth setup with manual configuration
oauth = OAuth()

# Google OAuth configuration
GOOGLE_CLIENT_ID = os.environ.get('GOOGLE_CLIENT_ID')
GOOGLE_CLIENT_SECRET = os.environ.get('GOOGLE_CLIENT_SECRET')
GOOGLE_REDIRECT_URI = os.environ.get('GOOGLE_REDIRECT_URI')

oauth.register(
    name='google',
    client_id=GOOGLE_CLIENT_ID,
    client_secret=GOOGLE_CLIENT_SECRET,
    server_metadata_url='https://accounts.google.com/.well-known/openid-configuration',
    client_kwargs={
        'scope': 'openid email profile'
    }
)

# Create a router with the /api prefix
api_router = APIRouter(prefix="/api")

# Security
security = HTTPBearer()

# Models
class User(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    email: str
    name: str
    password_hash: Optional[str] = None
    age_verified: bool = False
    is_admin: bool = False
    is_approved: bool = False
    created_at: datetime = Field(default_factory=datetime.utcnow)
    picture: Optional[str] = None
    session_token: Optional[str] = None

class UserRegister(BaseModel):
    email: str
    name: str
    password: str
    age_verified: bool

class UserLogin(BaseModel):
    email: str
    password: str

class Video(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str
    description: str
    category: str
    tags: List[str] = []
    filename: str
    file_path: str
    file_size: int
    duration: Optional[int] = None
    thumbnail: Optional[str] = None
    uploaded_by: str
    status: str = "pending"  # pending, approved, rejected
    created_at: datetime = Field(default_factory=datetime.utcnow)
    approved_by: Optional[str] = None
    approved_at: Optional[datetime] = None
    views: int = 0

class VideoUpload(BaseModel):
    title: str
    description: str
    category: str
    tags: List[str] = []

class VideoSearch(BaseModel):
    query: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    status: Optional[str] = None

# Utility functions
def hash_password(password: str) -> str:
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed: str) -> bool:
    return hash_password(password) == hashed

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=7)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET, algorithm="HS256")
    return encoded_jwt

def verify_token(token: str):
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    token = credentials.credentials
    payload = verify_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    user = await db.users.find_one({"id": payload["sub"]})
    if not user:
        raise HTTPException(status_code=401, detail="User not found")
    
    return User(**user)

async def get_admin_user(current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(status_code=403, detail="Admin access required")
    return current_user

# Routes
@api_router.post("/auth/register")
async def register(user_data: UserRegister):
    # Check if user exists
    existing_user = await db.users.find_one({"email": user_data.email})
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Create user
    user = User(
        email=user_data.email,
        name=user_data.name,
        password_hash=hash_password(user_data.password),
        age_verified=user_data.age_verified
    )
    
    await db.users.insert_one(user.dict())
    
    # Create token
    access_token = create_access_token({"sub": user.id})
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user.id,
            "email": user.email,
            "name": user.name,
            "age_verified": user.age_verified,
            "is_admin": user.is_admin
        }
    }

@api_router.post("/auth/login")
async def login(user_data: UserLogin):
    # Find user
    user = await db.users.find_one({"email": user_data.email})
    if not user or not verify_password(user_data.password, user["password_hash"]):
        raise HTTPException(status_code=400, detail="Invalid credentials")
    
    user_obj = User(**user)
    
    # Create token
    access_token = create_access_token({"sub": user_obj.id})
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user_obj.id,
            "email": user_obj.email,
            "name": user_obj.name,
            "age_verified": user_obj.age_verified,
            "is_admin": user_obj.is_admin
        }
    }

@api_router.get("/auth/profile")
async def get_profile(current_user: User = Depends(get_current_user)):
    return {
        "id": current_user.id,
        "email": current_user.email,
        "name": current_user.name,
        "age_verified": current_user.age_verified,
        "is_admin": current_user.is_admin,
        "is_approved": current_user.is_approved
    }

@api_router.get("/auth/google/login")
async def google_login(request: Request):
    """Initiate Google OAuth login"""
    redirect_uri = GOOGLE_REDIRECT_URI
    return await oauth.google.authorize_redirect(request, redirect_uri)

@api_router.get("/auth/google/callback")
async def google_callback(request: Request):
    """Handle Google OAuth callback"""
    try:
        token = await oauth.google.authorize_access_token(request)
        user_info = token.get('userinfo')
        
        if not user_info:
            raise HTTPException(status_code=400, detail="Failed to get user info from Google")
        
        # Check if user exists
        existing_user = await db.users.find_one({"email": user_info["email"]})
        
        if not existing_user:
            # Create new user
            user = User(
                email=user_info["email"],
                name=user_info["name"],
                picture=user_info.get("picture"),
                age_verified=True,  # Assume age verified through Google
            )
            await db.users.insert_one(user.dict())
        else:
            user = User(**existing_user)
            # Update picture if it's different
            if user.picture != user_info.get("picture"):
                await db.users.update_one(
                    {"id": user.id},
                    {"$set": {"picture": user_info.get("picture")}}
                )
                user.picture = user_info.get("picture")
        
        # Create access token
        access_token = create_access_token({"sub": user.id})
        
        # Redirect to frontend with token
        frontend_url = "http://localhost:3000"
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Authentication Success</title>
        </head>
        <body>
            <p>Authentication successful. This window will close automatically.</p>
            <script>
            window.opener.postMessage({{
                token: '{access_token}',
                user: {{
                    id: '{user.id}',
                    email: '{user.email}',
                    name: '{user.name}',
                    age_verified: {str(user.age_verified).lower()},
                    is_admin: {str(user.is_admin).lower()},
                    is_approved: {str(user.is_approved).lower()},
                    picture: '{user.picture or ''}'
                }}
            }}, '{frontend_url}');
            window.close();
            </script>
        </body>
        </html>
        """
        return HTMLResponse(content=html_content)
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Authentication failed: {str(e)}")

@api_router.post("/auth/google/token")
async def google_token_login(token_data: dict):
    """Verify Google ID token and login user"""
    try:
        # Verify the Google ID token
        id_info = id_token.verify_oauth2_token(
            token_data['credential'], 
            google_requests.Request(), 
            GOOGLE_CLIENT_ID
        )
        
        if id_info['iss'] not in ['accounts.google.com', 'https://accounts.google.com']:
            raise ValueError('Wrong issuer.')
        
        # Check if user exists
        existing_user = await db.users.find_one({"email": id_info["email"]})
        
        if not existing_user:
            # Create new user
            user = User(
                email=id_info["email"],
                name=id_info["name"],
                picture=id_info.get("picture"),
                age_verified=True,  # Assume age verified through Google
            )
            await db.users.insert_one(user.dict())
        else:
            user = User(**existing_user)
            # Update picture if it's different
            if user.picture != id_info.get("picture"):
                await db.users.update_one(
                    {"id": user.id},
                    {"$set": {"picture": id_info.get("picture")}}
                )
                user.picture = id_info.get("picture")
        
        # Create access token
        access_token = create_access_token({"sub": user.id})
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user.id,
                "email": user.email,
                "name": user.name,
                "age_verified": user.age_verified,
                "is_admin": user.is_admin,
                "picture": user.picture
            }
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid token: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Authentication failed: {str(e)}")

@api_router.post("/videos/upload")
async def upload_video(
    background_tasks: BackgroundTasks,
    title: str = Form(...),
    description: str = Form(...),
    category: str = Form(...),
    tags: str = Form(""),
    file: UploadFile = File(...),
    thumbnail: Optional[UploadFile] = File(None),
    current_user: User = Depends(get_current_user)
):
    # Validate file type
    if not file.content_type.startswith("video/"):
        raise HTTPException(status_code=400, detail="File must be a video")
    
    # Validate thumbnail file type if provided
    if thumbnail and not thumbnail.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="Thumbnail must be an image")
    
    # Create unique filename for video
    file_extension = file.filename.split(".")[-1]
    unique_filename = f"{uuid.uuid4()}.{file_extension}"
    file_path = uploads_dir / unique_filename
    
    # Save video file
    async with aiofiles.open(file_path, "wb") as buffer:
        content = await file.read()
        await buffer.write(content)
    
    # Handle thumbnail upload
    thumbnail_filename = None
    if thumbnail:
        thumbnail_extension = thumbnail.filename.split(".")[-1]
        thumbnail_filename = f"{uuid.uuid4()}_thumb.{thumbnail_extension}"
        thumbnail_path = uploads_dir / thumbnail_filename
        
        async with aiofiles.open(thumbnail_path, "wb") as buffer:
            thumbnail_content = await thumbnail.read()
            await buffer.write(thumbnail_content)
    
    # Parse tags
    tags_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
    
    # Create video record
    video = Video(
        title=title,
        description=description,
        category=category,
        tags=tags_list,
        filename=unique_filename,
        file_path=str(file_path),
        file_size=len(content),
        thumbnail=thumbnail_filename,
        uploaded_by=current_user.id,
        status="approved" if current_user.is_admin else "pending"
    )
    
    await db.videos.insert_one(video.dict())
    
    return {"message": "Video uploaded successfully", "video_id": video.id}

@api_router.get("/videos", response_model=List[Video])
async def get_videos(
    category: Optional[str] = None,
    status: Optional[str] = None,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
):
    query = {}
    current_user = None
    
    # Try to get current user if token is provided
    if credentials:
        try:
            payload = verify_token(credentials.credentials)
            if payload:
                user_data = await db.users.find_one({"id": payload["sub"]})
                if user_data:
                    current_user = User(**user_data)
        except:
            pass  # Continue as unauthenticated user
    
    # Admin users can see all videos, others only see approved videos
    if current_user and current_user.is_admin:
        # Admin can see all videos
        if status:
            query["status"] = status
    else:
        # Non-admin users can only see approved videos
        query["status"] = "approved"
    
    if category:
        query["category"] = category
    
    videos = await db.videos.find(query).sort("created_at", -1).to_list(100)
    return [Video(**video) for video in videos]

@api_router.get("/videos/{video_id}")
async def get_video(
    video_id: str, 
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
):
    current_user = None
    
    # Try to get current user if token is provided
    if credentials:
        try:
            payload = verify_token(credentials.credentials)
            if payload:
                user_data = await db.users.find_one({"id": payload["sub"]})
                if user_data:
                    current_user = User(**user_data)
        except:
            pass  # Continue as unauthenticated user
    
    video = await db.videos.find_one({"id": video_id})
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    video_obj = Video(**video)
    
    # Admin users can see all videos, others only see approved videos
    if not (current_user and current_user.is_admin) and video_obj.status != "approved":
        raise HTTPException(status_code=404, detail="Video not found")
    
    return video_obj

@api_router.get("/videos/{video_id}/stream")
async def stream_video(
    video_id: str, 
    token: Optional[str] = None, 
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
):
    current_user = None
    
    # Handle token-based authentication for video streaming (for HTML video elements)
    if token:
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
            user_id = payload.get("sub")
            if user_id:
                user_data = await db.users.find_one({"id": user_id})
                if user_data:
                    current_user = User(**user_data)
        except:
            pass  # Continue as unauthenticated user
    
    # Try to get user from Authorization header if no token provided
    elif credentials:
        try:
            payload = verify_token(credentials.credentials)
            if payload:
                user_data = await db.users.find_one({"id": payload["sub"]})
                if user_data:
                    current_user = User(**user_data)
        except:
            pass  # Continue as unauthenticated user
    
    video = await db.videos.find_one({"id": video_id})
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    video_obj = Video(**video)
    
    # Admin users can stream all videos, others only approved videos
    if not (current_user and current_user.is_admin) and video_obj.status != "approved":
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Increment views
    await db.videos.update_one(
        {"id": video_id},
        {"$inc": {"views": 1}}
    )
    
    # Stream file
    file_path = Path(video_obj.file_path)
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="Video file not found")
    
    def iterfile(file_path: Path):
        with open(file_path, "rb") as file_like:
            yield from file_like
    
    media_type = mimetypes.guess_type(str(file_path))[0] or "application/octet-stream"
    
    return StreamingResponse(
        iterfile(file_path),
        media_type=media_type,
        headers={"Content-Disposition": f"inline; filename={video_obj.filename}"}
    )

@api_router.get("/videos/{video_id}/thumbnail")
async def get_thumbnail(
    video_id: str,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
):
    video = await db.videos.find_one({"id": video_id})
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    video_obj = Video(**video)
    current_user = None
    
    # Try to get current user if token is provided
    if credentials:
        try:
            payload = verify_token(credentials.credentials)
            if payload:
                user_data = await db.users.find_one({"id": payload["sub"]})
                if user_data:
                    current_user = User(**user_data)
        except:
            pass  # Continue as unauthenticated user
    
    # Admin users can view all video thumbnails, others only approved videos
    if not (current_user and current_user.is_admin) and video_obj.status != "approved":
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Check if thumbnail exists
    if not video_obj.thumbnail:
        raise HTTPException(status_code=404, detail="Thumbnail not found")
    
    # Serve thumbnail file
    thumbnail_path = uploads_dir / video_obj.thumbnail
    if not thumbnail_path.exists():
        raise HTTPException(status_code=404, detail="Thumbnail file not found")
    
    def iterfile(file_path: Path):
        with open(file_path, "rb") as file_like:
            yield from file_like
    
    media_type = mimetypes.guess_type(str(thumbnail_path))[0] or "application/octet-stream"
    
    return StreamingResponse(
        iterfile(thumbnail_path),
        media_type=media_type,
        headers={"Content-Disposition": f"inline; filename={video_obj.thumbnail}"}
    )

@api_router.post("/videos/{video_id}/approve")
async def approve_video(video_id: str, admin_user: User = Depends(get_admin_user)):
    result = await db.videos.update_one(
        {"id": video_id},
        {
            "$set": {
                "status": "approved",
                "approved_by": admin_user.id,
                "approved_at": datetime.utcnow()
            }
        }
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Video not found")
    
    return {"message": "Video approved successfully"}

@api_router.post("/videos/{video_id}/reject")
async def reject_video(video_id: str, admin_user: User = Depends(get_admin_user)):
    result = await db.videos.update_one(
        {"id": video_id},
        {
            "$set": {
                "status": "rejected",
                "approved_by": admin_user.id,
                "approved_at": datetime.utcnow()
            }
        }
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Video not found")
    
    return {"message": "Video rejected successfully"}

@api_router.put("/videos/{video_id}")
async def update_video(video_id: str, video_data: dict, admin_user: User = Depends(get_admin_user)):
    # Get the current video to preserve file_path and other system fields
    current_video = await db.videos.find_one({"id": video_id})
    if not current_video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Only allow updating specific fields
    allowed_fields = ["title", "description", "category", "tags"]
    update_data = {k: v for k, v in video_data.items() if k in allowed_fields}
    
    if not update_data:
        raise HTTPException(status_code=400, detail="No valid fields to update")
    
    update_data["updated_at"] = datetime.utcnow()
    
    result = await db.videos.update_one(
        {"id": video_id},
        {"$set": update_data}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Return updated video
    updated_video = await db.videos.find_one({"id": video_id})
    return Video(**updated_video)

@api_router.delete("/videos/{video_id}")
async def delete_video(video_id: str, admin_user: User = Depends(get_admin_user)):
    # Get video to delete the file
    video = await db.videos.find_one({"id": video_id})
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Delete the video file
    file_path = Path(video["file_path"])
    if file_path.exists():
        try:
            file_path.unlink()
        except Exception as e:
            logger.warning(f"Failed to delete video file {file_path}: {e}")
    
    # Delete from database
    result = await db.videos.delete_one({"id": video_id})
    
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Video not found")
    
    return {"message": "Video deleted successfully"}

@api_router.get("/categories")
async def get_categories():
    # Only show categories from approved videos for public access
    categories = await db.videos.distinct("category", {"status": "approved"})
    return {"categories": categories}

@api_router.post("/search")
async def search_videos(
    search_data: VideoSearch, 
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
):
    query = {}
    current_user = None
    
    # Try to get current user if token is provided
    if credentials:
        try:
            payload = verify_token(credentials.credentials)
            if payload:
                user_data = await db.users.find_one({"id": payload["sub"]})
                if user_data:
                    current_user = User(**user_data)
        except:
            pass  # Continue as unauthenticated user
    
    # Admin users can search all videos, others only approved videos
    if current_user and current_user.is_admin:
        if search_data.status:
            query["status"] = search_data.status
    else:
        query["status"] = "approved"
    
    if search_data.category:
        query["category"] = search_data.category
    
    if search_data.tags:
        query["tags"] = {"$in": search_data.tags}
    
    if search_data.query:
        query["$or"] = [
            {"title": {"$regex": search_data.query, "$options": "i"}},
            {"description": {"$regex": search_data.query, "$options": "i"}},
            {"tags": {"$regex": search_data.query, "$options": "i"}}
        ]
    
    videos = await db.videos.find(query).sort("created_at", -1).to_list(100)
    return [Video(**video) for video in videos]

@api_router.get("/admin/users")
async def get_users(admin_user: User = Depends(get_admin_user)):
    users = await db.users.find().to_list(100)
    return [{"id": user["id"], "email": user["email"], "name": user["name"], "is_admin": user["is_admin"], "is_approved": user["is_approved"]} for user in users]

@api_router.post("/admin/users/{user_id}/approve")
async def approve_user(user_id: str, admin_user: User = Depends(get_admin_user)):
    result = await db.users.update_one(
        {"id": user_id},
        {"$set": {"is_approved": True}}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    
    return {"message": "User approved successfully"}

@api_router.post("/admin/users/{user_id}/make-admin")
async def make_admin(user_id: str, admin_user: User = Depends(get_admin_user)):
    result = await db.users.update_one(
        {"id": user_id},
        {"$set": {"is_admin": True}}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    
    return {"message": "User made admin successfully"}

# Include the router in the main app
app.include_router(api_router)

# Mount uploads directory for serving files
app.mount("/uploads", StaticFiles(directory="../uploads"), name="uploads")

app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@app.on_event("shutdown")
async def shutdown_db_client():
    client.close()