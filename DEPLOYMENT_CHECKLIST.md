# cPanel Deployment Checklist

Your updated codebase with Google OAuth functionality is ready for deployment. Follow this checklist to deploy to your cPanel hosting.

## ✅ Pre-Deployment Preparation Complete

- [x] Google OAuth endpoints implemented
- [x] Database compatibility fixed (SQLite → MySQL)
- [x] Deployment package created: `website-deployment.tar.gz`

## 🚀 Deployment Steps

### 1. Database Setup in cPanel

1. **Create Database:**
   - Go to cPanel → MySQL Databases
   - Create database: `your_username_appdb`
   - Create user: `your_username_appuser`
   - Add user to database with ALL PRIVILEGES
   - **Note down:** database name, username, password

### 2. Upload Files

1. **Upload deployment package:**
   - Go to cPanel → File Manager
   - Navigate to your home directory
   - Upload `website-deployment.tar.gz`
   - Extract the archive

### 3. Configure Backend for Production

1. **Update `backend-php/config/config.php`:**
   ```php
   // Comment out SQLite configuration:
   // define('DB_HOST', 'sqlite');
   // define('DB_NAME', '../database/local_test.db');
   
   // Uncomment and update MySQL configuration:
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'your_actual_database_name');
   define('DB_USER', 'your_actual_db_username');
   define('DB_PASS', 'your_actual_db_password');
   
   // Update domain settings:
   define('SITE_URL', 'https://yourdomain.com');
   define('API_URL', 'https://yourdomain.com/api');
   define('GOOGLE_REDIRECT_URI', 'https://yourdomain.com/api/auth/google/callback');
   
   // Generate new JWT secret (32+ characters):
   define('JWT_SECRET', 'your-new-secure-32-char-jwt-secret');
   ```

### 4. Setup File Structure

1. **Move backend files:**
   - Copy `backend-php/` contents to `public_html/api/`
   - Set permissions: 755 for directories, 644 for files

2. **Create uploads directory:**
   - Create `public_html/uploads/`
   - Set permissions to 755

### 5. Configure Frontend

1. **Update `frontend/.env`:**
   ```env
   # Comment out local development:
   # REACT_APP_BACKEND_URL=http://localhost:8080
   
   # Uncomment and update for production:
   REACT_APP_BACKEND_URL=https://yourdomain.com
   ```

2. **Build and upload frontend:**
   ```bash
   cd frontend
   npm install
   npm run build
   ```
   - Copy contents of `build/` folder to `public_html/`

### 6. Configure Web Server

1. **Create `.htaccess` in `public_html/`:**
   ```apache
   RewriteEngine On
   
   # API routes
   RewriteRule ^api/(.*)$ api/index.php [QSA,L]
   
   # Handle React Router
   RewriteCond %{REQUEST_FILENAME} !-f
   RewriteCond %{REQUEST_FILENAME} !-d
   RewriteRule . /index.html [L]
   
   # Security headers
   Header always set X-Content-Type-Options nosniff
   Header always set X-Frame-Options DENY
   Header always set X-XSS-Protection "1; mode=block"
   ```

### 7. Test Deployment

1. **Test API endpoints:**
   - `https://yourdomain.com/api/test`
   - `https://yourdomain.com/api/auth/google/login`
   - `https://yourdomain.com/api/auth/register`

2. **Test frontend:**
   - Visit `https://yourdomain.com`
   - Test Google OAuth login
   - Test user registration

## 🔧 Important Configuration Updates

### Google OAuth Setup
- Update Google Console with your production domain
- Add `https://yourdomain.com/api/auth/google/callback` to authorized redirect URIs

### Security Considerations
- Generate a strong JWT secret (32+ characters)
- Use strong database passwords
- Ensure file upload directory has proper permissions
- Set `DEBUG_MODE` to `false` in production

## 📁 Final File Structure

```
public_html/
├── index.html (React app)
├── static/ (React assets)
├── .htaccess (Main routing)
├── api/
│   ├── index.php
│   ├── config/
│   ├── models/
│   └── controllers/
└── uploads/
```

## 🆘 Troubleshooting

- **Database issues:** Check credentials and user permissions
- **API not working:** Verify .htaccess and PHP error logs
- **Google OAuth fails:** Check redirect URI in Google Console
- **File uploads fail:** Check directory permissions (755)

## ✨ New Features Deployed

- ✅ Google OAuth authentication
- ✅ JWT token-based sessions
- ✅ Popup-based OAuth flow
- ✅ MySQL database compatibility
- ✅ Secure file upload handling

Your codebase is now ready for production deployment with all the latest Google OAuth functionality!